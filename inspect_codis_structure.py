"""
檢查 CODiS 網站的實際結構
"""

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
import time

def inspect_codis_structure():
    """
    檢查 CODiS 網站的實際結構
    """
    print("檢查 CODiS 網站結構...")
    
    # 設定 Chrome 選項
    chrome_options = webdriver.ChromeOptions()
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--disable-gpu")
    chrome_options.add_argument("--disable-logging")
    chrome_options.add_argument("--disable-extensions")
    # 不使用無頭模式以便觀察
    
    service = Service(ChromeDriverManager().install())
    driver = webdriver.Chrome(service=service, options=chrome_options)
    wait = WebDriverWait(driver, 60)
    
    try:
        # 開啟 CODiS 網站
        print("正在開啟 CODiS 網站...")
        driver.get("https://codis.cwa.gov.tw/StationData")
        time.sleep(5)
        
        # 檢查頁面標題
        print(f"頁面標題: {driver.title}")
        
        # 選擇自動氣象站
        print("選擇自動氣象站...")
        stn_type = wait.until(
            EC.element_to_be_clickable(
                (By.XPATH, "//div[@class='form-check' and .//input[@value='auto_C0']]")
            )
        )
        stn_type.click()
        time.sleep(5)  # 等待頁面更新
        
        # 檢查所有 select 元素
        print("檢查所有 select 元素...")
        select_elements = driver.find_elements(By.TAG_NAME, "select")
        for i, select in enumerate(select_elements):
            try:
                select_id = select.get_attribute("id")
                select_name = select.get_attribute("name")
                select_class = select.get_attribute("class")
                print(f"Select {i+1}: id='{select_id}', name='{select_name}', class='{select_class}'")
                
                # 檢查選項
                options = select.find_elements(By.TAG_NAME, "option")
                print(f"  有 {len(options)} 個選項")
                if len(options) > 0 and len(options) <= 10:  # 只顯示少量選項
                    for j, option in enumerate(options[:5]):  # 只顯示前5個
                        value = option.get_attribute("value")
                        text = option.text
                        print(f"    選項 {j+1}: value='{value}', text='{text}'")
                        
            except Exception as e:
                print(f"檢查 select {i+1} 時發生錯誤: {e}")
        
        # 檢查所有包含 download 的元素
        print("\n檢查包含 download 的元素...")
        download_elements = driver.find_elements(By.XPATH, "//*[contains(@src, 'download') or contains(@class, 'download') or contains(@onclick, 'download')]")
        for i, element in enumerate(download_elements):
            try:
                tag = element.tag_name
                src = element.get_attribute("src")
                class_attr = element.get_attribute("class")
                onclick = element.get_attribute("onclick")
                print(f"Download元素 {i+1}: tag='{tag}', src='{src}', class='{class_attr}', onclick='{onclick}'")
            except Exception as e:
                print(f"檢查 download 元素 {i+1} 時發生錯誤: {e}")
        
        # 檢查所有 img 元素
        print("\n檢查所有 img 元素...")
        img_elements = driver.find_elements(By.TAG_NAME, "img")
        for i, img in enumerate(img_elements):
            try:
                src = img.get_attribute("src")
                alt = img.get_attribute("alt")
                if "download" in src.lower() or "download" in alt.lower():
                    print(f"Download圖片 {i+1}: src='{src}', alt='{alt}'")
            except Exception as e:
                print(f"檢查 img {i+1} 時發生錯誤: {e}")
        
        # 暫停讓用戶觀察
        print("\n程式將暫停60秒，請觀察瀏覽器並手動操作...")
        print("請嘗試手動選擇測站、年份、月份，然後觀察下載按鈕的位置")
        time.sleep(60)
        
    except Exception as e:
        print(f"檢查過程中發生錯誤: {e}")
        import traceback
        traceback.print_exc()
        
    finally:
        driver.quit()

if __name__ == "__main__":
    inspect_codis_structure()
