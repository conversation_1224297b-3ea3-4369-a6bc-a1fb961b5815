"""
測試多執行緒並行下載功能
用少數測站驗證並行下載是否正常運作
"""

from download_all_taiwan_stations import parallel_download_all_stations
from scrape_codis_date import cleanup_chrome_processes, generate_potential_stations

def test_parallel_download():
    """
    測試並行下載功能
    """
    print("=" * 60)
    print("測試多執行緒並行下載功能")
    print("=" * 60)
    
    # 清理開始前的殘留進程
    print("開始前清理瀏覽器進程...")
    cleanup_chrome_processes()
    
    # 獲取前5個測站進行測試
    all_stations = generate_potential_stations(test_mode=False)
    test_stations = all_stations[:5]  # 只測試前5個測站
    
    print(f"\n測試設定：")
    print(f"- 測試測站：{test_stations}")
    print(f"- 並行執行緒數：3")
    print(f"- 時間範圍：2024/01 到 2024/01")
    print()
    
    # 暫時修改 generate_potential_stations 來只返回測試測站
    import scrape_codis_date
    original_func = scrape_codis_date.generate_potential_stations
    
    def test_generate_stations(test_mode=False):
        return test_stations
    
    scrape_codis_date.generate_potential_stations = test_generate_stations
    
    try:
        print("開始並行下載測試...")
        results = parallel_download_all_stations(
            start_date="2024/01",
            end_date="2024/01",
            max_workers=3
        )
        
        print("\n測試完成！")
        
    except Exception as e:
        print(f"測試過程中發生錯誤: {e}")
    
    finally:
        # 恢復原始函數
        scrape_codis_date.generate_potential_stations = original_func
        
        # 清理結束後的殘留進程
        print("\n結束後清理瀏覽器進程...")
        cleanup_chrome_processes()
        print("測試結束")

if __name__ == "__main__":
    test_parallel_download()
