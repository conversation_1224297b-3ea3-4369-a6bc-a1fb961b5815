"""
CODiS 氣象資料爬蟲程式
===================

此程式用於自動化下載中央氣象署 CODiS 系統的氣象站月報表資料。
CODiS (Climate Observation Data Inquiry System) 是台灣官方的氣象觀測資料查詢系統。

主要功能：
1. 根據經緯度座標找到最近的氣象站
2. 自動獲取該氣象站的可用資料時間範圍
3. 批量下載指定時間範圍的月報表資料

技術架構：
- 使用 Selenium 模擬瀏覽器操作（因為 CODiS 是動態網頁，無法直接 API 存取）
- 使用 Haversine 公式計算地理距離
- 自動處理日期選擇和檔案下載
"""

import os
import time
from datetime import datetime, timedelta
import pandas as pd
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
from math import radians, sin, cos, sqrt, atan2
from pathlib import Path

# 設定資料目錄結構
# data/raw/weather: 存放原始下載的氣象資料
# data/interim/stations: 存放測站基本資訊
DATA_RAW = Path("data/raw")


def haversine(lat1, lon1, lat2, lon2):
    """
    使用 Haversine 公式計算地球表面兩點間的距離

    Haversine 公式是計算球面上兩點間最短距離的標準方法，
    考慮了地球的曲率，比直線距離更準確。

    Args:
        lat1, lon1: 第一個點的緯度和經度（十進位度數）
        lat2, lon2: 第二個點的緯度和經度（十進位度數）

    Returns:
        float: 兩點間的距離（公里）

    公式原理：
    1. 將度數轉換為弧度
    2. 計算緯度和經度差值
    3. 使用 Haversine 公式計算球面距離
    """
    R = 6371  # 地球半徑（公里）

    # 將度數轉換為弧度，因為三角函數需要弧度作為輸入
    dlat = radians(lat2 - lat1)  # 緯度差
    dlon = radians(lon2 - lon1)  # 經度差

    # Haversine 公式的核心計算
    # a 代表兩點間弦長的一半的平方
    a = (
        sin(dlat / 2) ** 2
        + cos(radians(lat1)) * cos(radians(lat2)) * sin(dlon / 2) ** 2
    )

    # c 是兩點間的角距離
    c = 2 * atan2(sqrt(a), sqrt(1 - a))

    # 距離 = 半徑 × 角距離
    return R * c


def get_all_auto_stations():
    """
    從 CODiS 網站獲取所有自動氣象站的資訊

    Returns:
        pd.DataFrame: 包含所有自動氣象站資訊的 DataFrame，欄位包括：
                     - 站號: 測站代號 (例如 'C0A980')
                     - 站名: 測站名稱
                     - 緯度: 測站緯度
                     - 經度: 測站經度
    """
    print("正在從 CODiS 網站獲取所有自動氣象站資訊...")

    # 設定 Chrome 瀏覽器選項
    chrome_options = webdriver.ChromeOptions()
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--disable-gpu")
    chrome_options.add_argument("--disable-web-security")
    chrome_options.add_argument("--allow-running-insecure-content")
    chrome_options.add_argument("--disable-logging")
    chrome_options.add_argument("--disable-extensions")
    chrome_options.add_argument("--disable-background-timer-throttling")
    chrome_options.add_argument("--disable-backgrounding-occluded-windows")
    chrome_options.add_argument("--disable-renderer-backgrounding")
    chrome_options.add_argument("--headless=new")  # 啟用無頭模式

    # 使用 webdriver-manager 自動管理 ChromeDriver
    service = Service(ChromeDriverManager().install())
    driver = webdriver.Chrome(service=service, options=chrome_options)
    wait = WebDriverWait(driver, 60)  # 增加等待時間到60秒

    try:
        # 開啟 CODiS 測站資料查詢頁面
        driver.get("https://codis.cwa.gov.tw/StationData")
        time.sleep(5)  # 增加等待時間

        # 選擇自動氣象站類型
        stn_type = wait.until(
            EC.element_to_be_clickable(
                (By.XPATH, "//div[@class='form-check' and .//input[@value='auto_C0']]")
            )
        )
        stn_type.click()
        time.sleep(3)  # 等待頁面更新測站選項

        # 點擊測站清單標籤
        station_list_tab = wait.until(
            EC.element_to_be_clickable(
                (By.XPATH, "//a[contains(text(), '測站清單')]")
            )
        )
        station_list_tab.click()
        time.sleep(2)

        # 等待測站清單載入
        wait.until(
            EC.presence_of_element_located(
                (By.XPATH, "//table[contains(@class, 'table')]")
            )
        )

        # 獲取所有測站資料行
        station_rows = driver.find_elements(
            By.XPATH, "//table[contains(@class, 'table')]//tbody//tr"
        )

        stations_data = []
        for row in station_rows:
            try:
                cells = row.find_elements(By.TAG_NAME, "td")
                if len(cells) >= 4:  # 確保有足夠的欄位
                    station_id = cells[0].text.strip()
                    station_name = cells[1].text.strip()
                    latitude = float(cells[2].text.strip())
                    longitude = float(cells[3].text.strip())

                    stations_data.append({
                        "站號": station_id,
                        "站名": station_name,
                        "緯度": latitude,
                        "經度": longitude
                    })
            except (ValueError, IndexError) as e:
                print(f"解析測站資料時發生錯誤: {e}")
                continue

        print(f"成功獲取 {len(stations_data)} 個自動氣象站資訊")
        return pd.DataFrame(stations_data)

    except Exception as e:
        print(f"獲取測站清單時發生錯誤: {e}")
        return pd.DataFrame()  # 回傳空的 DataFrame

    finally:
        driver.quit()


def get_stn_id(lon, lat):
    """
    根據給定的經緯度座標，找到最近的氣象測站ID

    這個函數的目的是將地理座標轉換為 CODiS 系統中的測站代號，
    因為 CODiS 系統需要特定的測站ID才能查詢資料。

    Args:
        lon (float): 目標位置的經度
        lat (float): 目標位置的緯度

    Returns:
        str: 最近測站的站號（例如 'C0A980'）

    工作流程：
    1. 檢查測站資料檔案是否存在
    2. 如果不存在，從 CODiS 網站獲取完整測站清單
    3. 計算目標位置與所有測站的距離
    4. 回傳距離最近的測站ID
    """
    min_dist = float("inf")  # 初始化最小距離為無限大
    nearest_station_id = None

    # 設定測站資料檔案路徑
    # 使用 interim 目錄存放處理過的中間資料
    stations_dir = Path("data/interim/stations")
    stations_file = stations_dir / "stations.csv"

    # 檢查測站資料檔案是否存在，如果不存在則從網站獲取
    if not stations_file.exists():
        stations_dir.mkdir(parents=True, exist_ok=True)  # 創建目錄結構

        # 從 CODiS 網站獲取完整的測站清單
        stations_df = get_all_auto_stations()

        if not stations_df.empty:
            # 儲存為 CSV 檔案，使用 utf-8-sig 編碼確保中文正確顯示
            stations_df.to_csv(stations_file, index=False, encoding="utf-8-sig")
            print(f"已儲存完整測站資料檔案: {stations_file}")
        else:
            # 如果無法從網站獲取，使用示例資料作為備案
            print("無法從網站獲取測站清單，使用示例資料...")
            sample_data = {
                "站號": ["C0A980", "C0B100", "C0C480"],  # CODiS 系統的測站代號
                "站名": ["桃山", "台北", "台中"],  # 測站中文名稱
                "緯度": [24.4327, 25.0330, 24.1477],  # 測站緯度座標
                "經度": [121.3038, 121.5654, 120.6736],  # 測站經度座標
            }
            pd.DataFrame(sample_data).to_csv(
                stations_file, index=False, encoding="utf-8-sig"
            )
            print(f"已創建示例測站資料檔案: {stations_file}")

    # 讀取測站資料
    df = pd.read_csv(stations_file)

    # 遍歷所有測站，計算與目標位置的距離
    for _, row in df.iterrows():
        try:
            # 從 CSV 讀取的資料可能是字串，需要轉換為浮點數
            station_lat = float(row["緯度"])
            station_lon = float(row["經度"])

            # 使用 Haversine 公式計算實際地理距離
            dist = haversine(lat, lon, station_lat, station_lon)

            # 如果這個測站比目前找到的最近測站更近，就更新記錄
            if dist < min_dist:
                min_dist = dist
                nearest_station_id = row["站號"]

        except ValueError:
            # 如果座標資料有問題（例如空值或非數字），跳過這個測站
            continue

    return nearest_station_id


def get_station_available_date_range(stn_id: str):
    """
    獲取指定測站在 CODiS 系統上可用的資料時間範圍

    這個函數的目的是自動探測測站的資料可用性，避免嘗試下載不存在的資料。
    CODiS 系統中不同測站的資料起始時間不同，有些測站資料較新，有些較舊。

    Args:
        stn_id (str): 測站ID，例如 'C0A980'

    Returns:
        tuple: (最早可用年月, 最晚可用年月)，格式為 'YYYY/MM'
               例如 ('1997/09', '2025/06')

    工作原理：
    1. 開啟 CODiS 網站並導航到資料查詢頁面
    2. 選擇指定的測站
    3. 進入日期選擇介面
    4. 檢查年份選擇器中可用的年份範圍
    5. 分別檢查最早年和最晚年的可用月份
    6. 回傳完整的時間範圍

    注意：這個過程需要實際操作網頁介面，因為 CODiS 沒有提供 API
    """
    # 設定 Chrome 瀏覽器選項
    chrome_options = webdriver.ChromeOptions()
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--disable-gpu")
    chrome_options.add_argument("--disable-web-security")
    chrome_options.add_argument("--allow-running-insecure-content")
    chrome_options.add_argument("--disable-logging")
    chrome_options.add_argument("--disable-extensions")
    chrome_options.add_argument("--disable-background-timer-throttling")
    chrome_options.add_argument("--disable-backgrounding-occluded-windows")
    chrome_options.add_argument("--disable-renderer-backgrounding")
    chrome_options.add_argument("--headless=new")  # 重新啟用無頭模式

    # 初始化 Chrome WebDriver
    service = Service(ChromeDriverManager().install())
    driver = webdriver.Chrome(service=service, options=chrome_options)

    # 設定等待物件，最多等待 60 秒讓頁面元素載入
    # 這是處理動態網頁的關鍵，確保元素完全載入後才進行操作
    wait = WebDriverWait(driver, 60)

    # 開啟 CODiS 測站資料查詢頁面
    driver.get("https://codis.cwa.gov.tw/StationData")
    time.sleep(1)  # 給頁面一點時間完成初始載入

    # 選擇測站類型：自動氣象站
    # CODiS 系統有多種測站類型：自動氣象站、雨量站、農業站等
    # 我們選擇自動氣象站是因為它提供最完整的氣象資料（溫度、濕度、風速等）
    stn_type = wait.until(
        EC.element_to_be_clickable(
            (By.XPATH, "//div[@class='form-check' and .//input[@value='auto_C0']]")
        )
    )
    stn_type.click()
    time.sleep(3)  # 等待頁面更新測站選項

    # 輸入測站代號到搜尋框
    # CODiS 系統使用測站ID來識別特定測站
    stn_input = wait.until(
        EC.presence_of_element_located((By.XPATH, "//input[@class='form-control']"))
    )
    stn_input.clear()  # 清除搜尋框中可能存在的內容
    stn_input.send_keys(stn_id)  # 輸入我們要查詢的測站ID

    # 等待地圖上出現測站標記，然後點擊
    # CODiS 使用 Leaflet 地圖庫，測站會以互動式標記顯示
    wait.until(
        EC.element_to_be_clickable(
            (By.XPATH, "//div[contains(@class, 'leaflet-interactive')]")
        )
    ).click()
    time.sleep(3)  # 等待地圖反應

    # 再次點擊地圖標記以確保選中測站
    # 有時需要點擊兩次才能正確選中測站
    wait.until(
        EC.element_to_be_clickable(
            (By.XPATH, "//div[contains(@class, 'leaflet-interactive')]")
        )
    ).click()
    time.sleep(3)

    # 點擊 '資料圖表展示' 按鈕
    wait.until(
        EC.element_to_be_clickable(
            (
                By.XPATH,
                f"//button[contains(@class, 'show_stn_tool') and contains(@data-stn_id, '{stn_id}')]",
            )
        )
    ).click()
    time.sleep(3)

    # 點擊月報表
    wait.until(
        EC.element_to_be_clickable(
            (
                By.XPATH,
                "//*[@id='main_content']/section[2]/div/div/aside/div[2]/div[2]/div[2]/div",
            )
        )
    ).click()

    # 點擊日期選單
    datetime_panel = wait.until(
        EC.element_to_be_clickable(
            (
                By.XPATH,
                '//*[@id="main_content"]/section[2]/div/div/section/div[6]/div[1]/div[1]/label/div/div[2]/div[1]',
            )
        )
    )
    datetime_panel.click()

    # 展開年份選擇器
    year_selector = wait.until(
        EC.element_to_be_clickable(
            (By.XPATH, "//div[contains(@class, 'vdatetime-popup__year')]")
        )
    )
    year_selector.click()

    # 抓可用年份
    year_elements = wait.until(
        EC.presence_of_all_elements_located(
            (
                By.XPATH,
                "//div[contains(@class, 'vdatetime-year-picker__item') and not(contains(@class, 'disabled'))]",
            )
        )
    )
    all_years = sorted(
        [int(el.text.strip()) for el in year_elements if el.text.strip().isdigit()]
    )
    earliest_year, latest_year = all_years[0], all_years[-1]

    # 點選最早年份 → 抓該年可用月份
    earliest_year_el = wait.until(
        EC.element_to_be_clickable(
            (
                By.XPATH,
                f"//div[contains(@class, 'vdatetime-year-picker__item') and contains(text(), '{earliest_year}')]",
            )
        )
    )
    driver.execute_script(
        "arguments[0].scrollIntoView({behavior: 'smooth', block: 'center'});",
        earliest_year_el,
    )
    earliest_year_el.click()

    month_elements = wait.until(
        EC.presence_of_all_elements_located(
            (
                By.XPATH,
                "//div[contains(@class, 'vdatetime-month-picker__item') and not(contains(@class, 'disabled'))]",
            )
        )
    )
    earliest_month = int(month_elements[0].text.strip().replace("月", ""))

    # 點選最新年份 → 抓該年可用月份
    datetime_panel.click()
    year_selector = wait.until(
        EC.element_to_be_clickable(
            (By.XPATH, "//div[contains(@class, 'vdatetime-popup__year')]")
        )
    )
    year_selector.click()

    latest_year_el = wait.until(
        EC.element_to_be_clickable(
            (
                By.XPATH,
                f"//div[contains(@class, 'vdatetime-year-picker__item') and contains(text(), '{latest_year}')]",
            )
        )
    )
    driver.execute_script(
        "arguments[0].scrollIntoView({behavior: 'smooth', block: 'center'});",
        latest_year_el,
    )
    latest_year_el.click()

    month_elements_latest = wait.until(
        EC.presence_of_all_elements_located(
            (
                By.XPATH,
                "//div[contains(@class, 'vdatetime-month-picker__item') and not(contains(@class, 'disabled'))]",
            )
        )
    )
    latest_month = int(month_elements_latest[-1].text.strip().replace("月", ""))

    driver.quit()

    start = f"{earliest_year}/{earliest_month:02d}"
    end = f"{latest_year}/{latest_month:02d}"
    print(f"氣象站 {stn_id} 可用範圍: {start} ~ {end}")
    return start, end


def download_codis(
    stn_id: str,
    start_date="2022/07",
    end_date="2025/06",
    download_dir=DATA_RAW / "weather",
):
    """
    批量下載指定氣象站的月報表資料

    這是主要的下載函數，會自動化整個下載流程：
    1. 檢查測站的資料可用性
    2. 調整下載範圍到可用範圍內
    3. 設定瀏覽器下載選項
    4. 逐月下載資料檔案

    為什麼需要逐月下載：
    - CODiS 系統不支援批量下載多個月份
    - 每個月的資料都是獨立的 CSV 檔案
    - 需要分別選擇年份和月份才能下載

    Args:
        stn_id (str): 氣象站ID，例如 'C0A980'
        start_date (str): 開始日期，格式 'YYYY/MM'，預設 '2022/07'
        end_date (str): 結束日期，格式 'YYYY/MM'，預設 '2025/06'
        download_dir (Path): 下載目錄，預設為 'data/raw/weather'

    Returns:
        None: 檔案會直接下載到指定目錄
    """
    # 確保下載目錄使用絕對路徑
    download_dir = Path(download_dir).resolve()
    os.makedirs(download_dir, exist_ok=True)
    print(f"檔案將下載到: {download_dir}")
    print(f"下載目錄絕對路徑: {download_dir.absolute()}")

    # 獲取氣象站可用的日期範圍
    print(f"正在獲取氣象站 {stn_id} 的可用日期範圍...")
    available_start, available_end = get_station_available_date_range(stn_id)

    if not available_start or not available_end:
        print(f"無法獲取氣象站 {stn_id} 的可用日期範圍，無法繼續下載。")
        return

    print(f"氣象站可用範圍: 從 {available_start} 到 {available_end}")

    # 檢查請求的日期範圍是否在可用範圍內
    start_dt = datetime.strptime(start_date, "%Y/%m")
    end_dt = datetime.strptime(end_date, "%Y/%m")
    available_start_dt = datetime.strptime(available_start, "%Y/%m")
    available_end_dt = datetime.strptime(available_end, "%Y/%m")

    # 調整日期範圍到可用範圍內
    if start_dt < available_start_dt:
        start_date = available_start
        start_dt = available_start_dt
        print(f"開始日期調整為氣象站可用的最早日期: {start_date}")

    if end_dt > available_end_dt:
        end_date = available_end
        end_dt = available_end_dt
        print(f"結束日期調整為氣象站可用的最晚日期: {end_date}")

    print(f"實際下載範圍: 從 {start_date} 到 {end_date}")

    # 計算總月數
    total_months = (
        (end_dt.year - start_dt.year) * 12 + (end_dt.month - start_dt.month) + 1
    )
    print(f"預計下載 {total_months} 個月的資料")

    # 設定 Chrome 瀏覽器的下載行為
    # 這些設定確保檔案能自動下載到指定目錄，而不會彈出下載對話框
    chrome_options = webdriver.ChromeOptions()
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--disable-gpu")
    chrome_options.add_argument("--disable-web-security")
    chrome_options.add_argument("--allow-running-insecure-content")
    chrome_options.add_argument("--disable-logging")
    chrome_options.add_argument("--disable-extensions")
    chrome_options.add_argument("--disable-background-timer-throttling")
    chrome_options.add_argument("--disable-backgrounding-occluded-windows")
    chrome_options.add_argument("--disable-renderer-backgrounding")
    chrome_options.add_argument("--headless=new")  # 啟用無頭模式

    prefs = {
        "download.default_directory": str(download_dir),  # 設定下載目錄
        "download.prompt_for_download": False,  # 不顯示下載確認對話框
        "download.directory_upgrade": True,  # 允許更改下載目錄
        "safebrowsing.enabled": True,  # 啟用安全瀏覽
    }
    chrome_options.add_experimental_option("prefs", prefs)

    # 初始化 Chrome WebDriver 並設定等待時間
    service = Service(ChromeDriverManager().install())
    driver = webdriver.Chrome(service=service, options=chrome_options)
    wait = WebDriverWait(driver, 60)  # 最多等待 60 秒讓元素載入

    # 日期處理 (使用調整後的日期範圍)
    # start_dt 和 end_dt 已經在上面定義過了

    driver.get("https://codis.cwa.gov.tw/StationData")

    # 選擇自動氣象站 雨量站跟農業站大概沒用 不勾
    stn_type = wait.until(
        EC.element_to_be_clickable(
            (By.XPATH, "//div[@class='form-check' and .//input[@value='auto_C0']]")
        )
    )
    stn_type.click()

    # 輸入氣象站ID
    stn_input = wait.until(
        EC.presence_of_element_located((By.XPATH, "//input[@class='form-control']"))
    )
    stn_input.clear()
    stn_input.send_keys(stn_id)
    time.sleep(2)

    # 點地圖標記圖釘
    wait.until(
        EC.element_to_be_clickable(
            (By.XPATH, "//div[contains(@class, 'leaflet-interactive')]")
        )
    ).click()
    time.sleep(2)

    # 點擊 '資料圖表展示' 按鈕
    wait.until(
        EC.element_to_be_clickable(
            (
                By.XPATH,
                f"//button[contains(@class, 'show_stn_tool') and contains(@data-stn_id, '{stn_id}')]",
            )
        )
    ).click()
    time.sleep(2)

    # 點月報表
    wait.until(
        EC.element_to_be_clickable(
            (
                By.XPATH,
                "//*[@id='main_content']/section[2]/div/div/aside/div[2]/div[2]/div[2]/div",
            )
        )
    ).click()
    time.sleep(2)

    # 開始逐月下載的主要迴圈
    # 從開始日期逐月遍歷到結束日期
    current_dt = start_dt
    current_month = 1  # 用於顯示進度

    while current_dt <= end_dt:
        year, month = current_dt.year, current_dt.month
        print(f"進度: {current_month}/{total_months} - 正在處理 {year}-{month:02d}")

        # 步驟1：開啟日期選擇面板
        # CODiS 使用自訂的日期選擇器，需要點擊特定元素來開啟
        datetime_panel = wait.until(
            EC.element_to_be_clickable(
                (
                    By.XPATH,
                    '//*[@id="main_content"]/section[2]/div/div/section/div[6]/div[1]/div[1]/label/div/div[2]/div[1]',
                )
            )
        )
        datetime_panel.click()
        time.sleep(1)  # 等待日期選擇器開啟

        # 步驟2：選擇年份
        # 首先點擊年份選擇器來展開年份列表
        year_selector = wait.until(
            EC.element_to_be_clickable(
                (By.XPATH, "//div[contains(@class, 'vdatetime-popup__year')]")
            )
        )
        year_selector.click()

        # 找到目標年份並點擊
        # 使用 XPath 來定位包含特定年份文字的元素
        target_year = wait.until(
            EC.element_to_be_clickable(
                (
                    By.XPATH,
                    f"//div[contains(@class, 'vdatetime-year-picker__item') and contains(text(), '{year}')]",
                )
            )
        )

        # 將目標年份滾動到視窗中央，確保可以點擊
        # 這是必要的，因為年份列表可能很長，目標年份可能不在可見範圍內
        driver.execute_script(
            "arguments[0].scrollIntoView({behavior: 'smooth', block: 'center'});",
            target_year,
        )
        target_year.click()

        # 步驟3：選擇月份
        # 找到目標月份並點擊（注意月份顯示格式為 "X月"）
        target_month = wait.until(
            EC.element_to_be_clickable(
                (
                    By.XPATH,
                    f"//div[contains(@class, 'vdatetime-month-picker__item') and contains(text(), '{month}月')]",
                )
            )
        )

        # 同樣需要滾動到可見範圍
        driver.execute_script(
            "arguments[0].scrollIntoView({behavior: 'smooth', block: 'center'});",
            target_month,
        )
        target_month.click()

        # 步驟4：執行下載
        # 點擊下載按鈕來下載當前選定月份的資料
        # 使用 img 標籤的 src 屬性來定位下載按鈕
        try:
            # 方法1：使用 img 標籤的 src 屬性
            download_btn = wait.until(
                EC.element_to_be_clickable(
                    (By.XPATH, "//img[@src='https://codis.cwa.gov.tw/Images/stn-tool/download.svg']")
                )
            )
            download_btn.click()
        except:
            try:
                # 方法2：使用包含 download.svg 的 img 標籤
                download_btn = wait.until(
                    EC.element_to_be_clickable(
                        (By.XPATH, "//img[contains(@src, 'download.svg')]")
                    )
                )
                download_btn.click()
            except:
                # 方法3：點擊包含下載圖片的父元素
                download_btn = wait.until(
                    EC.element_to_be_clickable(
                        (By.XPATH, "//img[contains(@src, 'download.svg')]/parent::*")
                    )
                )
                download_btn.click()
        print(f"下載 {stn_id}-{year}-{month:02d} 中...")

        # 等待檔案下載完成
        # 增加等待時間並檢查下載狀態
        print(f"等待檔案下載完成...")
        time.sleep(10)  # 增加等待時間

        # 檢查下載目錄中是否有新檔案
        csv_files = list(download_dir.glob("*.csv"))
        if csv_files:
            latest_file = max(csv_files, key=lambda f: f.stat().st_mtime)
            print(f"發現下載檔案: {latest_file.name} ({latest_file.stat().st_size} bytes)")
        else:
            print(f"警告: 在 {download_dir} 中未發現 CSV 檔案")

        # 步驟5：移動到下個月
        # 正確處理月份遞增，包括跨年的情況
        if current_dt.month == 12:
            # 如果是12月，下個月就是隔年1月
            current_dt = datetime(current_dt.year + 1, 1, 1)
        else:
            # 否則就是同年的下個月
            current_dt = datetime(current_dt.year, current_dt.month + 1, 1)
        current_month += 1  # 更新進度計數器

    # 關閉瀏覽器並清理資源
    driver.quit()
    print("瀏覽器已關閉。")


def download_all_stations(
    start_date="2022/07",
    end_date="2025/07",
    download_dir=DATA_RAW / "weather"
):
    """
    批量下載全台灣所有自動氣象站的月報表資料

    Args:
        start_date (str): 開始日期，格式 'YYYY/MM'，預設 '2022/07'
        end_date (str): 結束日期，格式 'YYYY/MM'，預設 '2025/07'
        download_dir (Path): 下載目錄，預設為 'data/raw/weather'
    """
    print("開始批量下載全台灣自動氣象站資料...")
    print(f"時間範圍: {start_date} 到 {end_date}")

    # 確保測站資料檔案存在
    stations_dir = Path("data/interim/stations")
    stations_file = stations_dir / "stations.csv"

    if not stations_file.exists():
        print("測站資料檔案不存在，正在獲取...")
        stations_dir.mkdir(parents=True, exist_ok=True)
        stations_df = get_all_auto_stations()

        if not stations_df.empty:
            stations_df.to_csv(stations_file, index=False, encoding="utf-8-sig")
            print(f"已儲存測站資料檔案: {stations_file}")
        else:
            print("無法獲取測站清單，程式終止")
            return

    # 讀取測站清單
    stations_df = pd.read_csv(stations_file)
    total_stations = len(stations_df)
    print(f"共有 {total_stations} 個自動氣象站需要下載")

    # 創建下載目錄
    os.makedirs(download_dir, exist_ok=True)

    # 記錄下載結果
    success_count = 0
    failed_stations = []

    # 逐個測站下載
    for index, row in stations_df.iterrows():
        station_id = row["站號"]
        station_name = row["站名"]

        print(f"\n進度: {index + 1}/{total_stations}")
        print(f"正在處理測站: {station_id} ({station_name})")

        try:
            # 為每個測站創建子目錄
            station_dir = download_dir / station_id
            os.makedirs(station_dir, exist_ok=True)

            # 下載該測站的資料
            download_codis(
                stn_id=station_id,
                start_date=start_date,
                end_date=end_date,
                download_dir=station_dir
            )

            success_count += 1
            print(f"測站 {station_id} ({station_name}) 下載完成")

        except Exception as e:
            print(f"測站 {station_id} ({station_name}) 下載失敗: {e}")
            failed_stations.append(f"{station_id} ({station_name})")
            continue

    # 輸出下載結果摘要
    print(f"\n=== 下載完成摘要 ===")
    print(f"成功下載: {success_count}/{total_stations} 個測站")

    if failed_stations:
        print(f"失敗測站數量: {len(failed_stations)}")
        print("失敗測站清單:")
        for station in failed_stations:
            print(f"  - {station}")

    print(f"所有檔案已下載到: {download_dir}")


if __name__ == "__main__":
    """
    程式主入口點

    修改為批量下載全台灣所有自動氣象站的月報表資料
    時間範圍：2022年7月到2025年7月
    """
    # 批量下載全台灣所有自動氣象站的月報表資料
    download_all_stations(
        start_date="2022/07",
        end_date="2025/07"
    )
