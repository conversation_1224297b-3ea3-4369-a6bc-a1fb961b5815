import os
import time
from datetime import datetime, timedelta
import pandas as pd
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from math import radians, sin, cos, sqrt, atan2
from pathlib import Path
import threading

# 設定資料目錄
DATA_RAW = Path("data/raw")

# 全域鎖，用於控制瀏覽器視窗的創建
browser_lock = threading.Lock()

def haversine(lat1, lon1, lat2, lon2):
    R = 6371
    dlat = radians(lat2 - lat1)
    dlon = radians(lon2 - lon1)
    a = sin(dlat/2)**2 + cos(radians(lat1)) * cos(radians(lat2)) * sin(dlon/2)**2
    c = 2 * atan2(sqrt(a), sqrt(1 - a))
    return R * c

def get_stn_id(lon, lat):
    """回傳最近測站的站號"""
    min_dist = float("inf")
    nearest_station_id = None

    df = pd.read_csv(r"data\interim\stations\stations.csv")

    for _, row in df.iterrows():
        try:
            station_lat = float(row["緯度"])
            station_lon = float(row["經度"])
            dist = haversine(lat, lon, station_lat, station_lon)
            if dist < min_dist:
                min_dist = dist
                nearest_station_id = row["站號"]
        except ValueError:
            continue

    return nearest_station_id

def get_station_available_date_range(stn_id: str):
    """
    回傳指定測站(stn_id)在CODiS上可選取的最早和最晚年月 (YYYY/MM)。
    """
    chrome_options = webdriver.ChromeOptions()
    # chrome_options.add_argument("--headless=new") # 不開瀏覽器
    driver = webdriver.Chrome(options=chrome_options)
    wait = WebDriverWait(driver, 20)

    driver.get("https://codis.cwa.gov.tw/StationData")
    time.sleep(1)

    # 選擇自動氣象站
    stn_type = wait.until(
        EC.element_to_be_clickable((By.XPATH, "//div[@class='form-check' and .//input[@value='auto_C0']]"))
    )
    stn_type.click()
    time.sleep(3)

    # 輸入測站代號
    stn_input = wait.until(EC.presence_of_element_located((By.XPATH, "//input[@class='form-control']")))
    stn_input.clear()
    stn_input.send_keys(stn_id)
    wait.until(EC.element_to_be_clickable((By.XPATH, "//div[contains(@class, 'leaflet-interactive')]"))).click()
    time.sleep(3)

    # 點地圖標記圖釘
    wait.until(EC.element_to_be_clickable((By.XPATH, "//div[contains(@class, 'leaflet-interactive')]"))).click()
    time.sleep(3)

    # 點擊 '資料圖表展示' 按鈕
    wait.until(EC.element_to_be_clickable((By.XPATH, f"//button[contains(@class, 'show_stn_tool') and contains(@data-stn_id, '{stn_id}')]"))).click()
    time.sleep(3)

    # 點擊月報表
    wait.until(EC.element_to_be_clickable((By.XPATH, "//*[@id='main_content']/section[2]/div/div/aside/div[2]/div[2]/div[2]/div"))).click()

    # 點擊日期選單
    datetime_panel = wait.until(
        EC.element_to_be_clickable((By.XPATH, '//*[@id="main_content"]/section[2]/div/div/section/div[6]/div[1]/div[1]/label/div/div[2]/div[1]'))
    )
    datetime_panel.click()

    # 展開年份選擇器
    year_selector = wait.until(EC.element_to_be_clickable((By.XPATH, "//div[contains(@class, 'vdatetime-popup__year')]")))
    year_selector.click()

    # 抓可用年份
    year_elements = wait.until(
        EC.presence_of_all_elements_located((By.XPATH, "//div[contains(@class, 'vdatetime-year-picker__item') and not(contains(@class, 'disabled'))]"))
    )
    all_years = sorted([int(el.text.strip()) for el in year_elements if el.text.strip().isdigit()])
    earliest_year, latest_year = all_years[0], all_years[-1]

    # 點選最早年份 → 抓該年可用月份
    earliest_year_el = wait.until(
        EC.element_to_be_clickable((By.XPATH, f"//div[contains(@class, 'vdatetime-year-picker__item') and contains(text(), '{earliest_year}')]"))
    )
    driver.execute_script("arguments[0].scrollIntoView({behavior: 'smooth', block: 'center'});", earliest_year_el)
    earliest_year_el.click()

    month_elements = wait.until(
        EC.presence_of_all_elements_located((By.XPATH, "//div[contains(@class, 'vdatetime-month-picker__item') and not(contains(@class, 'disabled'))]"))
    )
    earliest_month = int(month_elements[0].text.strip().replace('月', ''))

    # 點選最新年份 → 抓該年可用月份
    datetime_panel.click()
    year_selector = wait.until(EC.element_to_be_clickable((By.XPATH, "//div[contains(@class, 'vdatetime-popup__year')]")))
    year_selector.click()

    latest_year_el = wait.until(
        EC.element_to_be_clickable((By.XPATH, f"//div[contains(@class, 'vdatetime-year-picker__item') and contains(text(), '{latest_year}')]"))
    )
    driver.execute_script("arguments[0].scrollIntoView({behavior: 'smooth', block: 'center'});", latest_year_el)
    latest_year_el.click()

    month_elements_latest = wait.until(
        EC.presence_of_all_elements_located((By.XPATH, "//div[contains(@class, 'vdatetime-month-picker__item') and not(contains(@class, 'disabled'))]"))
    )
    latest_month = int(month_elements_latest[-1].text.strip().replace('月', ''))

    driver.quit()

    start = f"{earliest_year}/{earliest_month:02d}"
    end = f"{latest_year}/{latest_month:02d}"
    print(f"氣象站 {stn_id} 可用範圍: {start} ~ {end}")
    return start, end


def download_codis(stn_id: str, start_date="2022/07", end_date="2025/07", download_dir=DATA_RAW / 'weather'):
    """
    下載指定氣象站ID(stn_id)從 start_date 到 end_date 的月報表資料。
    start_date, end_date 格式: 'YYYY/MM' 例如 '2022/07'
    """
    os.makedirs(download_dir, exist_ok=True)
    print(f"檔案將下載到: {download_dir}")

    # 使用指定的日期範圍，不再動態獲取
    print(f"下載範圍: 從 {start_date} 到 {end_date}")
    start, end = start_date, end_date

    # 設定 Selenium
    chrome_options = webdriver.ChromeOptions()
    # 確保使用絕對路徑
    abs_download_dir = str(Path(download_dir).resolve())
    prefs = {
        "download.default_directory": abs_download_dir,
        "download.prompt_for_download": False,
        "download.directory_upgrade": True,
        "safebrowsing.enabled": True
    }
    chrome_options.add_experimental_option("prefs", prefs)

    # 減少視窗跳轉的設定
    chrome_options.add_argument("--disable-web-security")
    chrome_options.add_argument("--disable-features=VizDisplayCompositor")
    chrome_options.add_argument("--disable-extensions")
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--disable-gpu")
    chrome_options.add_argument("--disable-background-timer-throttling")
    chrome_options.add_argument("--disable-backgrounding-occluded-windows")
    chrome_options.add_argument("--disable-renderer-backgrounding")
    chrome_options.add_argument("--disable-features=TranslateUI")
    chrome_options.add_argument("--disable-ipc-flooding-protection")

    # 設定視窗位置和大小，避免跳轉
    chrome_options.add_argument("--window-size=1200,800")
    chrome_options.add_argument("--window-position=100,100")

    print(f"設定下載目錄為: {abs_download_dir}")
    driver = webdriver.Chrome(options=chrome_options)
    wait = WebDriverWait(driver, 20)

    # 日期處理
    start_dt = datetime.strptime(start, "%Y/%m")
    end_dt = datetime.strptime(end, "%Y/%m")

    driver.get("https://codis.cwa.gov.tw/StationData")

    # 選擇自動氣象站 雨量站跟農業站大概沒用 不勾
    stn_type = wait.until(
        EC.element_to_be_clickable((By.XPATH, "//div[@class='form-check' and .//input[@value='auto_C0']]"))
    )
    stn_type.click()

    # 輸入氣象站ID
    stn_input = wait.until(EC.presence_of_element_located((By.XPATH, "//input[@class='form-control']")))
    stn_input.clear()
    stn_input.send_keys(stn_id)
    time.sleep(2)

    # 點地圖標記圖釘
    wait.until(EC.element_to_be_clickable((By.XPATH, "//div[contains(@class, 'leaflet-interactive')]"))).click()
    time.sleep(2)

    # 點擊 '資料圖表展示' 按鈕
    wait.until(EC.element_to_be_clickable((By.XPATH, f"//button[contains(@class, 'show_stn_tool') and contains(@data-stn_id, '{stn_id}')]"))).click()
    time.sleep(2)

    # 點月報表
    wait.until(EC.element_to_be_clickable((By.XPATH, "//*[@id='main_content']/section[2]/div/div/aside/div[2]/div[2]/div[2]/div"))).click()
    time.sleep(2)

    current_dt = start_dt
    while current_dt <= end_dt:
        year, month = current_dt.year, current_dt.month

        # 開啟日期選單
        datetime_panel = wait.until(
            EC.element_to_be_clickable((By.XPATH, '//*[@id="main_content"]/section[2]/div/div/section/div[6]/div[1]/div[1]/label/div/div[2]/div[1]'))
        )
        datetime_panel.click()
        time.sleep(1)

        # 點擊年份
        year_selector = wait.until(EC.element_to_be_clickable((By.XPATH, "//div[contains(@class, 'vdatetime-popup__year')]")))
        year_selector.click()
        target_year = wait.until(EC.element_to_be_clickable((By.XPATH, f"//div[contains(@class, 'vdatetime-year-picker__item') and contains(text(), '{year}')]")))
        driver.execute_script("arguments[0].scrollIntoView({behavior: 'smooth', block: 'center'});", target_year)
        target_year.click()

        # 點擊月份
        target_month = wait.until(EC.element_to_be_clickable((By.XPATH, f"//div[contains(@class, 'vdatetime-month-picker__item') and contains(text(), '{month}月')]")))
        driver.execute_script("arguments[0].scrollIntoView({behavior: 'smooth', block: 'center'});", target_month)
        target_month.click()

        # 點擊下載
        download_btn = wait.until(EC.element_to_be_clickable((By.XPATH, "//*[@id='main_content']/section[2]/div/div/section/div[6]/div[1]/div[2]/div")))
        download_btn.click()
        print(f"下載 {stn_id}-{year}-{month:02d} 中...")
        time.sleep(5)  # 等待檔案下載
        
        # 到下個月
        next_month = (current_dt.month % 12) + 1
        next_year = current_dt.year + (current_dt.month // 12)
        current_dt = datetime(next_year, next_month, 1)

    driver.quit()
    print("瀏覽器已關閉。")

def generate_potential_stations(test_mode=False):
    """
    生成自動氣象站代號清單
    基於中央氣象署官方測站清單
    """
    # 從官方網站獲取的所有 C0 開頭的自動氣象站代號
    # 資料來源：https://hdps.cwa.gov.tw/static/state.html#existing_station
    all_stations = [
        "C0A520", "C0A530", "C0A550", "C0A570", "C0A640", "C0A770", "C0A860", "C0A870",
        "C0A890", "C0A931", "C0A940", "C0A950", "C0A970", "C0A980", "C0A9C0", "C0A9F0",
        "C0AC40", "C0AC60", "C0AC70", "C0AC80", "C0ACA0", "C0AD10", "C0AD30", "C0AD40",
        "C0AD50", "C0AG80", "C0AH00", "C0AH10", "C0AH30", "C0AH40", "C0AH50", "C0AH70",
        "C0AH80", "C0AH90", "C0AI00", "C0AI10", "C0AI20", "C0AI30", "C0AI40", "C0AJ20",
        "C0AJ30", "C0AJ40", "C0AJ50", "C0AJ60", "C0AJ70", "C0AJ80", "C0AJ90", "C0AK10",
        "C0AK30", "C0B010", "C0B020", "C0B040", "C0B050", "C0B060", "C0C460", "C0C480",
        "C0C490", "C0C590", "C0C620", "C0C630", "C0C650", "C0C660", "C0C670", "C0C680",
        "C0C700", "C0C710", "C0C720", "C0C730", "C0C740", "C0C750", "C0C790", "C0C800",
        "C0D360", "C0D430", "C0D480", "C0D540", "C0D550", "C0D560", "C0D580", "C0D590",
        "C0D650", "C0D660", "C0D670", "C0D680", "C0D690", "C0D700", "C0D750", "C0D760",
        "C0E420", "C0E430", "C0E550", "C0E570", "C0E590", "C0E610", "C0E730", "C0E740",
        "C0E750", "C0E780", "C0E791", "C0E810", "C0E820", "C0E830", "C0E850", "C0E870",
        "C0E910", "C0E920", "C0E930", "C0E940", "C0E950", "C0E960", "C0F0A0", "C0F0B0",
        "C0F0C0", "C0F0D0", "C0F0E0", "C0F850", "C0F970", "C0F9I0", "C0F9K0", "C0F9L0",
        "C0F9M0", "C0F9N0", "C0F9O0", "C0F9P0", "C0F9Q0", "C0F9R0", "C0F9S0", "C0F9T0",
        "C0F9U0", "C0F9V0", "C0F9X0", "C0F9Y0", "C0F9Z0", "C0FA10", "C0FA20", "C0FA30",
        "C0FA40", "C0FA50", "C0FA60", "C0FA70", "C0FA80", "C0FA90", "C0FB00", "C0FB10",
        "C0FB20", "C0FB30", "C0FB40", "C0FB70", "C0G620", "C0G650", "C0G660", "C0G720",
        "C0G730", "C0G740", "C0G770", "C0G780", "C0G800", "C0G810", "C0G820", "C0G830",
        "C0G860", "C0G880", "C0G890", "C0G900", "C0G910", "C0G920", "C0G940", "C0G950",
        "C0G960", "C0G970", "C0G9B0", "C0H890", "C0H960", "C0H990", "C0H9A0", "C0H9C0",
        "C0I010", "C0I080", "C0I110", "C0I360", "C0I370", "C0I380", "C0I390", "C0I410",
        "C0I420", "C0I460", "C0I480", "C0I490", "C0I520", "C0I530", "C0I540", "C0K250",
        "C0K280", "C0K291", "C0K330", "C0K390", "C0K400", "C0K410", "C0K420", "C0K430",
        "C0K440", "C0K450", "C0K460", "C0K470", "C0K480", "C0K500", "C0K510", "C0K530",
        "C0K550", "C0K560", "C0K580", "C0K590", "C0K600", "C0M520", "C0M530", "C0M640",
        "C0M650", "C0M660", "C0M670", "C0M680", "C0M690", "C0M700", "C0M710", "C0M720",
        "C0M730", "C0M740", "C0M750", "C0M760", "C0M770", "C0M780", "C0M790", "C0M800",
        "C0M810", "C0M820", "C0M850", "C0M860", "C0M880", "C0N010", "C0N020", "C0N030",
        "C0N040", "C0N050", "C0O830", "C0O840", "C0O860", "C0O900", "C0O960", "C0O970",
        "C0O980", "C0O990", "C0R100", "C0R130", "C0R140", "C0R150", "C0R160", "C0R190",
        "C0R220", "C0R240", "C0R260", "C0R270", "C0R280", "C0R320", "C0R341", "C0R350",
        "C0R440", "C0R470", "C0R480", "C0R490", "C0R520", "C0R540", "C0R550", "C0R560",
        "C0R570", "C0R580", "C0R590", "C0R600", "C0R620", "C0R640", "C0R650", "C0R660",
        "C0R670", "C0R680", "C0R690", "C0R700", "C0R710", "C0R720", "C0R730", "C0R741"
    ]

    if test_mode:
        # 測試模式：只返回前3個測站
        return ["C0A980", "C0C480", "C0E420"]

    return all_stations

def download_all_stations(start_date="2022/07", end_date="2025/07", test_mode=False):
    """
    批量下載全台灣所有自動氣象站的月報表資料
    """
    print("開始批量下載全台灣自動氣象站資料...")
    print(f"時間範圍: {start_date} 到 {end_date}")

    # 生成可能的測站清單
    potential_stations = generate_potential_stations(test_mode=test_mode)
    total_stations = len(potential_stations)
    if test_mode:
        print(f"測試模式：共有 {total_stations} 個已知測站")
    else:
        print(f"完整模式：共有 {total_stations} 個潛在測站需要嘗試")

    # 設定基礎下載目錄
    base_download_dir = DATA_RAW / "weather" / "all_stations"

    # 記錄結果
    success_count = 0
    failed_stations = []

    # 逐個測站嘗試下載
    for index, station_id in enumerate(potential_stations):
        print(f"\n進度: {index + 1}/{total_stations}")
        print(f"正在嘗試測站: {station_id}")

        try:
            # 為每個測站創建子目錄
            station_dir = base_download_dir / station_id

            # 嘗試下載該測站的資料
            download_codis(
                stn_id=station_id,
                start_date=start_date,
                end_date=end_date,
                download_dir=station_dir
            )

            # 檢查是否有檔案被下載（檢查下載目錄和預設下載目錄）
            csv_files_target = list(station_dir.glob("*.csv")) if station_dir.exists() else []
            csv_files_default = list(Path("D:/Users/<USER>/Downloads").glob(f"{station_id}-*.csv"))

            if csv_files_target or csv_files_default:
                success_count += 1
                print(f"測站 {station_id} 下載成功")

                # 如果檔案在預設下載目錄，移動到目標目錄
                if csv_files_default and not csv_files_target:
                    os.makedirs(station_dir, exist_ok=True)
                    for file in csv_files_default:
                        target_file = station_dir / file.name
                        file.rename(target_file)
                        print(f"  移動檔案: {file.name} -> {target_file}")
            else:
                print(f"測站 {station_id} 可能不存在或無資料")
                failed_stations.append(f"{station_id} - 無資料")

        except Exception as e:
            print(f"測站 {station_id} 下載失敗: {e}")
            failed_stations.append(f"{station_id} - 錯誤: {str(e)[:50]}")
            continue

    # 輸出結果摘要
    print(f"\n=== 批量下載完成摘要 ===")
    print(f"成功下載: {success_count}/{total_stations} 個測站")

    if failed_stations:
        print(f"失敗測站數量: {len(failed_stations)}")
        print("部分失敗測站清單:")
        for station in failed_stations[:10]:  # 只顯示前10個
            print(f"  - {station}")
        if len(failed_stations) > 10:
            print(f"  ... 還有 {len(failed_stations) - 10} 個失敗測站")

    print(f"所有檔案已下載到: {base_download_dir}")

if __name__ == "__main__":
    # 選擇運行模式
    mode = input("選擇運行模式 (1: 測試單一測站, 2: 批量下載已知測站, 3: 批量下載所有測站): ")

    if mode == "1":
        # 測試單一測站
        stn_id = get_stn_id(121.3038, 24.4327)
        download_codis(stn_id, start_date="2024/01", end_date="2024/01")
    elif mode == "2":
        # 批量下載已知測站（測試模式）
        download_all_stations(start_date="2024/01", end_date="2024/01", test_mode=True)
    elif mode == "3":
        # 批量下載所有測站（完整模式）
        print("警告：這將嘗試下載大量測站資料，可能需要很長時間！")
        confirm = input("確定要繼續嗎？(y/N): ")
        if confirm.lower() == 'y':
            download_all_stations(start_date="2022/07", end_date="2025/07", test_mode=False)
        else:
            print("已取消批量下載")
    else:
        print("無效的選擇，預設執行測試模式")
        stn_id = get_stn_id(121.3038, 24.4327)
        download_codis(stn_id, start_date="2024/01", end_date="2024/01")
