import os
import time
from datetime import datetime, timedelta
import pandas as pd
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from math import radians, sin, cos, sqrt, atan2
from pathlib import Path

# 設定資料目錄
DATA_RAW = Path("data/raw")

def haversine(lat1, lon1, lat2, lon2):
    R = 6371
    dlat = radians(lat2 - lat1)
    dlon = radians(lon2 - lon1)
    a = sin(dlat/2)**2 + cos(radians(lat1)) * cos(radians(lat2)) * sin(dlon/2)**2
    c = 2 * atan2(sqrt(a), sqrt(1 - a))
    return R * c

def get_stn_id(lon, lat):
    """回傳最近測站的站號"""
    min_dist = float("inf")
    nearest_station_id = None

    df = pd.read_csv(r"data\interim\stations\stations.csv")

    for _, row in df.iterrows():
        try:
            station_lat = float(row["緯度"])
            station_lon = float(row["經度"])
            dist = haversine(lat, lon, station_lat, station_lon)
            if dist < min_dist:
                min_dist = dist
                nearest_station_id = row["站號"]
        except ValueError:
            continue

    return nearest_station_id

def get_station_available_date_range(stn_id: str):
    """
    回傳指定測站(stn_id)在CODiS上可選取的最早和最晚年月 (YYYY/MM)。
    """
    chrome_options = webdriver.ChromeOptions()
    # chrome_options.add_argument("--headless=new") # 不開瀏覽器
    driver = webdriver.Chrome(options=chrome_options)
    wait = WebDriverWait(driver, 20)

    driver.get("https://codis.cwa.gov.tw/StationData")
    time.sleep(1)

    # 選擇自動氣象站
    stn_type = wait.until(
        EC.element_to_be_clickable((By.XPATH, "//div[@class='form-check' and .//input[@value='auto_C0']]"))
    )
    stn_type.click()
    time.sleep(3)

    # 輸入測站代號
    stn_input = wait.until(EC.presence_of_element_located((By.XPATH, "//input[@class='form-control']")))
    stn_input.clear()
    stn_input.send_keys(stn_id)
    wait.until(EC.element_to_be_clickable((By.XPATH, "//div[contains(@class, 'leaflet-interactive')]"))).click()
    time.sleep(3)

    # 點地圖標記圖釘
    wait.until(EC.element_to_be_clickable((By.XPATH, "//div[contains(@class, 'leaflet-interactive')]"))).click()
    time.sleep(3)

    # 點擊 '資料圖表展示' 按鈕
    wait.until(EC.element_to_be_clickable((By.XPATH, f"//button[contains(@class, 'show_stn_tool') and contains(@data-stn_id, '{stn_id}')]"))).click()
    time.sleep(3)

    # 點擊月報表
    wait.until(EC.element_to_be_clickable((By.XPATH, "//*[@id='main_content']/section[2]/div/div/aside/div[2]/div[2]/div[2]/div"))).click()

    # 點擊日期選單
    datetime_panel = wait.until(
        EC.element_to_be_clickable((By.XPATH, '//*[@id="main_content"]/section[2]/div/div/section/div[6]/div[1]/div[1]/label/div/div[2]/div[1]'))
    )
    datetime_panel.click()

    # 展開年份選擇器
    year_selector = wait.until(EC.element_to_be_clickable((By.XPATH, "//div[contains(@class, 'vdatetime-popup__year')]")))
    year_selector.click()

    # 抓可用年份
    year_elements = wait.until(
        EC.presence_of_all_elements_located((By.XPATH, "//div[contains(@class, 'vdatetime-year-picker__item') and not(contains(@class, 'disabled'))]"))
    )
    all_years = sorted([int(el.text.strip()) for el in year_elements if el.text.strip().isdigit()])
    earliest_year, latest_year = all_years[0], all_years[-1]

    # 點選最早年份 → 抓該年可用月份
    earliest_year_el = wait.until(
        EC.element_to_be_clickable((By.XPATH, f"//div[contains(@class, 'vdatetime-year-picker__item') and contains(text(), '{earliest_year}')]"))
    )
    driver.execute_script("arguments[0].scrollIntoView({behavior: 'smooth', block: 'center'});", earliest_year_el)
    earliest_year_el.click()

    month_elements = wait.until(
        EC.presence_of_all_elements_located((By.XPATH, "//div[contains(@class, 'vdatetime-month-picker__item') and not(contains(@class, 'disabled'))]"))
    )
    earliest_month = int(month_elements[0].text.strip().replace('月', ''))

    # 點選最新年份 → 抓該年可用月份
    datetime_panel.click()
    year_selector = wait.until(EC.element_to_be_clickable((By.XPATH, "//div[contains(@class, 'vdatetime-popup__year')]")))
    year_selector.click()

    latest_year_el = wait.until(
        EC.element_to_be_clickable((By.XPATH, f"//div[contains(@class, 'vdatetime-year-picker__item') and contains(text(), '{latest_year}')]"))
    )
    driver.execute_script("arguments[0].scrollIntoView({behavior: 'smooth', block: 'center'});", latest_year_el)
    latest_year_el.click()

    month_elements_latest = wait.until(
        EC.presence_of_all_elements_located((By.XPATH, "//div[contains(@class, 'vdatetime-month-picker__item') and not(contains(@class, 'disabled'))]"))
    )
    latest_month = int(month_elements_latest[-1].text.strip().replace('月', ''))

    driver.quit()

    start = f"{earliest_year}/{earliest_month:02d}"
    end = f"{latest_year}/{latest_month:02d}"
    print(f"氣象站 {stn_id} 可用範圍: {start} ~ {end}")
    return start, end


def download_codis(stn_id: str, start_date="2022/07", end_date="2025/07", download_dir=DATA_RAW / 'weather'):
    """
    下載指定氣象站ID(stn_id)從 start_date 到 end_date 的月報表資料。
    start_date, end_date 格式: 'YYYY/MM' 例如 '2022/07'
    """
    os.makedirs(download_dir, exist_ok=True)
    print(f"檔案將下載到: {download_dir}")

    # 使用指定的日期範圍，不再動態獲取
    print(f"下載範圍: 從 {start_date} 到 {end_date}")
    start, end = start_date, end_date

    # 設定 Selenium
    chrome_options = webdriver.ChromeOptions()
    # 確保使用絕對路徑
    abs_download_dir = str(Path(download_dir).resolve())
    prefs = {
        "download.default_directory": abs_download_dir,
        "download.prompt_for_download": False,
        "download.directory_upgrade": True,
        "safebrowsing.enabled": True
    }
    chrome_options.add_experimental_option("prefs", prefs)
    print(f"設定下載目錄為: {abs_download_dir}")
    driver = webdriver.Chrome(options=chrome_options)
    wait = WebDriverWait(driver, 20)

    # 日期處理
    start_dt = datetime.strptime(start, "%Y/%m")
    end_dt = datetime.strptime(end, "%Y/%m")

    driver.get("https://codis.cwa.gov.tw/StationData")

    # 選擇自動氣象站 雨量站跟農業站大概沒用 不勾
    stn_type = wait.until(
        EC.element_to_be_clickable((By.XPATH, "//div[@class='form-check' and .//input[@value='auto_C0']]"))
    )
    stn_type.click()

    # 輸入氣象站ID
    stn_input = wait.until(EC.presence_of_element_located((By.XPATH, "//input[@class='form-control']")))
    stn_input.clear()
    stn_input.send_keys(stn_id)
    time.sleep(2)

    # 點地圖標記圖釘
    wait.until(EC.element_to_be_clickable((By.XPATH, "//div[contains(@class, 'leaflet-interactive')]"))).click()
    time.sleep(2)

    # 點擊 '資料圖表展示' 按鈕
    wait.until(EC.element_to_be_clickable((By.XPATH, f"//button[contains(@class, 'show_stn_tool') and contains(@data-stn_id, '{stn_id}')]"))).click()
    time.sleep(2)

    # 點月報表
    wait.until(EC.element_to_be_clickable((By.XPATH, "//*[@id='main_content']/section[2]/div/div/aside/div[2]/div[2]/div[2]/div"))).click()
    time.sleep(2)

    current_dt = start_dt
    while current_dt <= end_dt:
        year, month = current_dt.year, current_dt.month

        # 開啟日期選單
        datetime_panel = wait.until(
            EC.element_to_be_clickable((By.XPATH, '//*[@id="main_content"]/section[2]/div/div/section/div[6]/div[1]/div[1]/label/div/div[2]/div[1]'))
        )
        datetime_panel.click()
        time.sleep(1)

        # 點擊年份
        year_selector = wait.until(EC.element_to_be_clickable((By.XPATH, "//div[contains(@class, 'vdatetime-popup__year')]")))
        year_selector.click()
        target_year = wait.until(EC.element_to_be_clickable((By.XPATH, f"//div[contains(@class, 'vdatetime-year-picker__item') and contains(text(), '{year}')]")))
        driver.execute_script("arguments[0].scrollIntoView({behavior: 'smooth', block: 'center'});", target_year)
        target_year.click()

        # 點擊月份
        target_month = wait.until(EC.element_to_be_clickable((By.XPATH, f"//div[contains(@class, 'vdatetime-month-picker__item') and contains(text(), '{month}月')]")))
        driver.execute_script("arguments[0].scrollIntoView({behavior: 'smooth', block: 'center'});", target_month)
        target_month.click()

        # 點擊下載
        download_btn = wait.until(EC.element_to_be_clickable((By.XPATH, "//*[@id='main_content']/section[2]/div/div/section/div[6]/div[1]/div[2]/div")))
        download_btn.click()
        print(f"下載 {stn_id}-{year}-{month:02d} 中...")
        time.sleep(5)  # 等待檔案下載
        
        # 到下個月
        next_month = (current_dt.month % 12) + 1
        next_year = current_dt.year + (current_dt.month // 12)
        current_dt = datetime(next_year, next_month, 1)

    driver.quit()
    print("瀏覽器已關閉。")

def generate_potential_stations(test_mode=False):
    """
    生成可能的自動氣象站代號清單
    基於 CODiS 系統的 C0 開頭規則
    """
    stations = []

    # 已知存在的測站
    known_stations = ["C0A980", "C0B100", "C0C480"]
    stations.extend(known_stations)

    if test_mode:
        # 測試模式：只返回已知存在的測站
        return stations

    # 生成可能的測站代號
    # C0 開頭表示自動氣象站
    for prefix in ['C0A', 'C0B', 'C0C', 'C0D', 'C0E', 'C0F', 'C0G', 'C0H', 'C0I', 'C0J']:
        for suffix in range(100, 1000, 10):  # 每10個號碼取一個，避免太多無效嘗試
            station_id = f"{prefix}{suffix:03d}"
            if station_id not in stations:  # 避免重複
                stations.append(station_id)

    return stations

def download_all_stations(start_date="2022/07", end_date="2025/07", test_mode=False):
    """
    批量下載全台灣所有自動氣象站的月報表資料
    """
    print("開始批量下載全台灣自動氣象站資料...")
    print(f"時間範圍: {start_date} 到 {end_date}")

    # 生成可能的測站清單
    potential_stations = generate_potential_stations(test_mode=test_mode)
    total_stations = len(potential_stations)
    if test_mode:
        print(f"測試模式：共有 {total_stations} 個已知測站")
    else:
        print(f"完整模式：共有 {total_stations} 個潛在測站需要嘗試")

    # 設定基礎下載目錄
    base_download_dir = DATA_RAW / "weather" / "all_stations"

    # 記錄結果
    success_count = 0
    failed_stations = []

    # 逐個測站嘗試下載
    for index, station_id in enumerate(potential_stations):
        print(f"\n進度: {index + 1}/{total_stations}")
        print(f"正在嘗試測站: {station_id}")

        try:
            # 為每個測站創建子目錄
            station_dir = base_download_dir / station_id

            # 嘗試下載該測站的資料
            download_codis(
                stn_id=station_id,
                start_date=start_date,
                end_date=end_date,
                download_dir=station_dir
            )

            # 檢查是否有檔案被下載（檢查下載目錄和預設下載目錄）
            csv_files_target = list(station_dir.glob("*.csv")) if station_dir.exists() else []
            csv_files_default = list(Path("D:/Users/<USER>/Downloads").glob(f"{station_id}-*.csv"))

            if csv_files_target or csv_files_default:
                success_count += 1
                print(f"測站 {station_id} 下載成功")

                # 如果檔案在預設下載目錄，移動到目標目錄
                if csv_files_default and not csv_files_target:
                    os.makedirs(station_dir, exist_ok=True)
                    for file in csv_files_default:
                        target_file = station_dir / file.name
                        file.rename(target_file)
                        print(f"  移動檔案: {file.name} -> {target_file}")
            else:
                print(f"測站 {station_id} 可能不存在或無資料")
                failed_stations.append(f"{station_id} - 無資料")

        except Exception as e:
            print(f"測站 {station_id} 下載失敗: {e}")
            failed_stations.append(f"{station_id} - 錯誤: {str(e)[:50]}")
            continue

    # 輸出結果摘要
    print(f"\n=== 批量下載完成摘要 ===")
    print(f"成功下載: {success_count}/{total_stations} 個測站")

    if failed_stations:
        print(f"失敗測站數量: {len(failed_stations)}")
        print("部分失敗測站清單:")
        for station in failed_stations[:10]:  # 只顯示前10個
            print(f"  - {station}")
        if len(failed_stations) > 10:
            print(f"  ... 還有 {len(failed_stations) - 10} 個失敗測站")

    print(f"所有檔案已下載到: {base_download_dir}")

if __name__ == "__main__":
    # 選擇運行模式
    mode = input("選擇運行模式 (1: 測試單一測站, 2: 批量下載已知測站, 3: 批量下載所有測站): ")

    if mode == "1":
        # 測試單一測站
        stn_id = get_stn_id(121.3038, 24.4327)
        download_codis(stn_id, start_date="2024/01", end_date="2024/01")
    elif mode == "2":
        # 批量下載已知測站（測試模式）
        download_all_stations(start_date="2024/01", end_date="2024/01", test_mode=True)
    elif mode == "3":
        # 批量下載所有測站（完整模式）
        print("警告：這將嘗試下載大量測站資料，可能需要很長時間！")
        confirm = input("確定要繼續嗎？(y/N): ")
        if confirm.lower() == 'y':
            download_all_stations(start_date="2022/07", end_date="2025/07", test_mode=False)
        else:
            print("已取消批量下載")
    else:
        print("無效的選擇，預設執行測試模式")
        stn_id = get_stn_id(121.3038, 24.4327)
        download_codis(stn_id, start_date="2024/01", end_date="2024/01")
