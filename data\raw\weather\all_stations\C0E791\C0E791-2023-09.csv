﻿"觀測時間(day)","測站氣壓(hPa)","測站最高氣壓(hPa)","測站最高氣壓時間(LST)","測站最低氣壓(hPa)","測站最低氣壓時間(LST)","氣溫(℃)","最高氣溫(℃)","最高氣溫時間(LST)","最低氣溫(℃)","最低氣溫時間(LST)","相對溼度(%)","最小相對溼度(%)","最小相對溼度時間(LST)","風速(m/s)","風向(360degree)","最大瞬間風(m/s)","最大瞬間風風向(360degree)","最大瞬間風風速時間(LST)","降水量(mm)"
"ObsTime","StnPres","StnPresMax","StnPresMaxTime","StnPresMin","StnPresMinTime","Temperature","T Max","T Max Time","<PERSON> <PERSON>","T Min Time","RH","RHMin","RHMinTime","WS","WD","WSGust","WDGust","WGustTime","Precp"
"01","964.6","965.7","2023/09/01 10:02:00","963.4","2023/09/01 15:12:00","27.5","32.3","2023/09/01 12:10:00","25.1","2023/09/01 05:41:00","86","65","2023/09/01 11:51:00","1.2","293","6.3","292","2023/09/01 13:01:00","8.5"
"02","963.2","965.2","2023/09/02 08:52:00","961.5","2023/09/02 15:31:00","28.1","32.6","2023/09/02 15:51:00","24.5","2023/09/02 04:11:00","84","66","2023/09/02 13:25:00","1.0","17","4.7","287","2023/09/02 11:45:00","0.0"
"03","956.2","962.4","2023/09/03 00:01:00","948.9","2023/09/03 19:01:00","26.7","28.0","2023/09/03 08:42:00","24.9","2023/09/04 00:00:00","89","74","2023/09/03 20:21:00","1.1","69","9.0","61","2023/09/03 17:57:00","3.0"
"04","958.7","965.2","2023/09/04 23:31:00","953.4","2023/09/04 05:25:00","26.5","29.7","2023/09/04 14:33:00","24.2","2023/09/04 05:51:00","90","73","2023/09/04 14:50:00","1.3","52","6.7","47","2023/09/04 20:46:00","6.5"
"05","967.2","969.8","2023/09/05 20:41:00","964.0","2023/09/05 01:51:00","27.1","32.8","2023/09/05 13:52:00","24.6","2023/09/05 20:07:00","88","58","2023/09/05 13:53:00","0.9","332","6.5","287","2023/09/05 16:20:00","61.5"
"06","970.2","971.7","2023/09/06 21:33:00","968.7","2023/09/06 03:41:00","26.2","31.5","2023/09/06 11:26:00","24.1","2023/09/06 05:56:00","91","66","2023/09/06 11:26:00","0.9","278","6.5","61","2023/09/06 16:19:00","52.0"
"07","969.8","971.7","2023/09/07 09:32:00","968.2","2023/09/07 15:33:00","27.0","31.6","2023/09/07 14:01:00","23.9","2023/09/07 06:12:00","87","67","2023/09/07 14:54:00","0.8","224","5.3","214","2023/09/07 18:01:00","1.0"
"08","970.1","971.6","2023/09/08 08:43:00","968.7","2023/09/08 14:54:00","26.8","30.1","2023/09/08 12:51:00","24.3","2023/09/08 22:06:00","86","71","2023/09/08 12:35:00","0.9","332","5.0","36","2023/09/08 13:31:00","0.0"
"09","969.8","971.0","2023/09/09 08:51:00","968.1","2023/09/09 15:11:00","26.0","30.1","2023/09/09 10:13:00","24.2","2023/09/09 04:31:00","87","66","2023/09/09 10:17:00","1.2","216","7.4","236","2023/09/09 16:02:00","1.0"
"10","969.1","970.9","2023/09/10 21:32:00","967.6","2023/09/10 16:52:00","26.8","32.1","2023/09/10 11:01:00","24.0","2023/09/10 03:02:00","84","58","2023/09/10 11:01:00","1.0","289","6.4","265","2023/09/10 12:02:00","0.0"
"11","969.4","970.7","2023/09/11 21:02:00","967.8","2023/09/11 14:21:00","26.6","30.6","2023/09/11 13:35:00","22.9","2023/09/11 06:02:00","81","64","2023/09/11 08:37:00","1.2","287","7.5","292","2023/09/11 13:59:00","0.0"
"12","969.5","970.9","2023/09/12 09:41:00","967.8","2023/09/12 14:41:00","26.3","30.8","2023/09/12 12:21:00","22.1","2023/09/12 05:45:00","80","55","2023/09/12 10:11:00","1.1","292","5.5","291","2023/09/12 12:42:00","0.0"
"13","969.6","970.6","2023/09/13 09:52:00","968.4","2023/09/13 16:24:00","26.4","30.6","2023/09/13 10:46:00","23.6","2023/09/13 05:51:00","80","59","2023/09/13 09:44:00","1.2","146","6.1","303","2023/09/13 10:16:00","0.0"
"14","970.4","972.2","2023/09/14 22:03:00","969.4","2023/09/14 03:42:00","26.4","30.1","2023/09/14 13:11:00","23.0","2023/09/14 05:41:00","81","67","2023/09/14 14:36:00","0.9","295","5.3","306","2023/09/14 10:32:00","0.0"
"15","972.0","974.4","2023/09/15 21:46:00","970.1","2023/09/15 14:22:00","26.5","32.0","2023/09/15 12:56:00","22.7","2023/09/15 05:43:00","81","60","2023/09/15 10:18:00","1.2","279","7.4","275","2023/09/15 14:47:00","1.5"
"16","973.3","975.1","2023/09/16 08:46:00","971.6","2023/09/16 15:02:00","26.7","32.1","2023/09/16 13:04:00","22.6","2023/09/16 04:23:00","78","54","2023/09/16 09:21:00","1.0","295","6.2","272","2023/09/16 13:50:00","0.0"
"17","972.7","974.7","2023/09/17 10:38:00","971.2","2023/09/17 15:42:00","27.4","31.4","2023/09/17 12:22:00","24.5","2023/09/17 05:38:00","80","59","2023/09/17 11:22:00","1.0","276","5.5","303","2023/09/17 13:01:00","0.0"
"18","973.2","974.5","2023/09/18 09:11:00","971.7","2023/09/18 14:31:00","27.2","31.6","2023/09/18 13:02:00","23.4","2023/09/18 06:01:00","81","61","2023/09/18 10:36:00","1.1","323","6.2","311","2023/09/18 14:12:00","0.0"
"19","972.9","974.7","2023/09/19 07:52:00","971.3","2023/09/19 15:31:00","27.7","32.9","2023/09/19 13:03:00","23.2","2023/09/19 06:11:00","78","56","2023/09/19 13:03:00","1.0","282","5.4","282","2023/09/19 11:51:00","0.0"
"20","972.0","973.8","2023/09/20 09:41:00","970.3","2023/09/20 16:43:00","27.9","31.4","2023/09/20 11:43:00","25.0","2023/09/20 05:51:00","78","62","2023/09/20 08:11:00","1.2","144","6.1","297","2023/09/20 13:10:00","0.0"
"21","972.2","973.9","2023/09/21 08:55:00","970.9","2023/09/21 15:42:00","27.3","30.6","2023/09/21 16:41:00","24.8","2023/09/21 05:51:00","80","66","2023/09/21 07:42:00","1.1","287","5.7","292","2023/09/21 13:58:00","0.0"
"22","971.3","972.8","2023/09/22 09:47:00","969.4","2023/09/22 14:45:00","28.0","32.8","2023/09/22 12:02:00","23.7","2023/09/22 04:58:00","79","57","2023/09/22 11:54:00","1.2","261","6.0","291","2023/09/22 11:35:00","0.0"
"23","971.0","972.2","2023/09/23 20:51:00","969.4","2023/09/23 15:13:00","27.4","32.7","2023/09/23 14:22:00","24.2","2023/09/23 05:45:00","84","61","2023/09/23 14:22:00","1.2","279","5.4","284","2023/09/23 09:53:00","19.5"
"24","971.4","973.5","2023/09/24 09:41:00","969.8","2023/09/24 14:43:00","27.2","32.1","2023/09/24 14:01:00","24.0","2023/09/24 05:24:00","86","64","2023/09/24 12:15:00","1.0","58","5.4","81","2023/09/24 17:45:00","0.0"
"25","971.3","973.2","2023/09/25 08:51:00","969.5","2023/09/25 16:01:00","28.2","32.0","2023/09/25 13:14:00","24.2","2023/09/25 05:18:00","81","64","2023/09/25 10:53:00","1.0","20","6.3","44","2023/09/25 13:34:00","0.0"
"26","971.5","973.4","2023/09/26 08:15:00","970.0","2023/09/26 16:01:00","27.9","33.0","2023/09/26 16:01:00","25.3","2023/09/26 05:42:00","82","60","2023/09/26 16:01:00","1.0","302","5.9","282","2023/09/26 11:32:00","0.0"
"27","970.8","972.3","2023/09/27 23:12:00","969.1","2023/09/27 14:55:00","27.8","31.5","2023/09/27 11:26:00","24.6","2023/09/27 05:32:00","81","66","2023/09/27 13:45:00","1.1","298","6.5","304","2023/09/27 12:26:00","0.0"
"28","971.8","973.4","2023/09/28 22:11:00","970.3","2023/09/28 15:01:00","27.8","32.8","2023/09/28 13:23:00","24.0","2023/09/28 06:02:00","82","56","2023/09/28 14:11:00","1.1","246","4.7","305","2023/09/28 10:08:00","0.0"
"29","972.0","973.6","2023/09/29 09:11:00","970.1","2023/09/29 15:21:00","28.9","33.4","2023/09/29 12:41:00","25.2","2023/09/29 05:32:00","78","56","2023/09/29 12:41:00","1.0","71","4.9","279","2023/09/29 12:30:00","0.0"
"30","970.3","972.3","2023/09/30 08:26:00","968.4","2023/09/30 15:32:00","27.9","32.0","2023/09/30 15:02:00","24.9","2023/09/30 05:40:00","82","64","2023/09/30 14:22:00","1.1","292","5.4","337","2023/09/30 15:29:00","0.0"