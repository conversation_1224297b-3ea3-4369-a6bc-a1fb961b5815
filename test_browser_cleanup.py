"""
測試瀏覽器清理功能
用於驗證是否能正確處理瀏覽器視窗的開啟和關閉
"""

from scrape_codis_date import download_codis, cleanup_chrome_processes

def test_single_download():
    """
    測試單一測站下載，驗證瀏覽器是否正確關閉
    """
    print("=" * 50)
    print("測試單一測站下載功能")
    print("=" * 50)
    
    # 清理開始前的殘留進程
    print("開始前清理...")
    cleanup_chrome_processes()
    
    try:
        # 測試下載一個測站
        print("\n開始下載測試...")
        download_codis("C0A980", start_date="2024/01", end_date="2024/01")
        print("下載完成")
        
    except Exception as e:
        print(f"下載過程中發生錯誤: {e}")
    
    finally:
        # 清理結束後的殘留進程
        print("\n結束後清理...")
        cleanup_chrome_processes()
        print("測試完成")

if __name__ == "__main__":
    test_single_download()
