"""
農業氣象站資料下載腳本
下載2023/7~2025/6月報表資料
支援1-12個並行視窗，具備強制超時控制

功能特點：
1. 包含完整的農業站代號清單（共127個農業站）
2. 自動取消預設勾選的"署屬有人站"選項
3. 自動選擇"農業站"選項
4. 支援測試模式和完整模式
5. 強制超時控制，避免無限運行

使用方法：
1. 運行程式：python download_agricultural_stations.py
2. 選擇測試模式或完整模式
3. 選擇並行視窗數量（1-12個）
4. 程式會自動下載並整理檔案到 agricultural_weather_station 目錄
"""

import os
import time
from datetime import datetime
from pathlib import Path
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait, Select
from selenium.webdriver.support import expected_conditions as EC
from concurrent.futures import ThreadPoolExecutor, as_completed

# 農業氣象站代號清單（根據農業站.jpg圖片更新）
AGRICULTURAL_STATIONS = [
    # 雲林地區
    "V2K620",  # 麥寮合作社
    "V2K610",  # 大庄合作社

    # 桃園地區
    "V2C260",  # 八德蔬果
    "V2C250",  # 八德合作社

    # 南投地區
    "U2HA50",  # 臺大竹山
    "U2HA40",  # 臺大內茅埔
    "U2HA30",  # 臺大和社
    "U2H480",  # 溪頭

    # 苗栗地區
    "K2F750",  # 種苗改良場
    "K2E710",  # 苗改生物防治研究中心
    "K2E360",  # 苗栗農改場

    # 高雄、嘉義地區
    "G2P820",  # 農試鳳山分所
    "G2M350",  # 農試溪口農場
    "G2L020",  # 農試嘉義分所
    "G2F820",  # 農試所(霧峰)
    "G2AI50",  # 關渡

    # 台東、屏東地區
    "E2S980",  # 林試太麻里1
    "E2S960",  # 林試太麻里2
    "E2P990",  # 林試扇平站
    "E2P980",  # 林試六龜中心
    "E2K600",  # 四湖植物園
    "E2HA20",  # 林試畢祿溪站
    "E2H360",  # 蓮華池

    # 嘉義、雲林沿海地區
    "CAQ030",  # 崎峰國小
    "CAN140",  # 六官養殖協會
    "CAN130",  # 水試所海水繁養殖中心
    "CAL110",  # 布袋國中
    "CAJ050",  # 海口故事園區
    "CAH030",  # 茶改場竹圍站
    "CAG100",  # 王功漁港

    # 澎湖、金門地區
    "C2W230",  # 畜試所澎湖
    "C2W030",  # 金門農試所

    # 高雄地區
    "C2V310",  # 美濃
    "C2V260",  # 月眉
    "C2V250",  # 甲仙

    # 屏東地區
    "C2R970",  # 屏科大
    "C2R170",  # 屏東

    # 台南地區
    "C20950",  # 安南
    "C20930",  # 玉井
    "C20810",  # 曾文
    "C2N160",  # 西拉雅風管處

    # 嘉義山區
    "C2M970",  # 碧湖
    "C2M960",  # 外寮
    "C2M950",  # 太和
    "C2M940",  # 日野賀
    "C2M930",  # 石卓
    "C2M920",  # 朴子農改
    "C2M910",  # 嘉義大學
    "C2M620",  # 瑞里
    "C2M410",  # 馬頭山

    # 雲林地區
    "C2K630",  # 荷苞
    "C2K620",  # 馬光農場
    "C2K610",  # 草嶺石壁
    "C2K240",  # 草嶺

    # 南投地區
    "C21090",  # 鳳凰
    "C2H9W0",  # 大坪頂農水
    "C2H9UO",  # 鳳鵬
    "C2H9TO",  # 名間竹園
    "C2H9S0",  # 龍南
    "C2H9RO",  # 卓社
    "C2H9Q0",  # 北東眼山
    "C2H9PO",  # 伊拿谷
    "C2H9NO",  # 仁愛東眼
    "C2H9M0",  # 發祥
    "C2H9LO",  # 馬烈霸
    "C2H9JO",  # 中台
    "C2H9H0",  # 苗改南投蜂場
    "C2H9G0",  # 百勝
    "C2H9FO",  # 柑林
    "C2H9E0",  # 國姓南港
    "C2H9D0",  # 三隻寮
    "C2H950",  # 中寮

    # 彰化地區
    "C2G9A0",  # 畜試所彰化
    "C2G980",  # 田頭村
    "C2G870",  # 芳苑
    "C2G840",  # 北斗
    "C2G640",  # 鹿港

    # 台中地區
    "C2FB60",  # 頭櫃山
    "C2FB50",  # 出雲
    "C2FA00",  # 烏石坑
    "C2F9A0",  # 中竹林
    "C2F990",  # 摩天嶺
    "C2F930",  # 大甲
    "C2F860",  # 梨山
    "C2F000",  # 大肚

    # 苗栗地區
    "C2E970",  # 八甲
    "C2E880",  # 三義
    "C2E540",  # 龍溪
    "C2E520",  # 大湖

    # 新竹地區
    "C2D740",  # 屯原
    "C2D730",  # 寶山農場
    "C2D720",  # 關西工作站

    # 桃園地區
    "C2C410",  # 中央大學

    # 新北、基隆地區
    "C2A920",  # 富貴角
    "C2A880",  # 福隆
    "C2A660",  # 瑞芳
    "C2A650",  # 火燒寮
    "C2A560",  # 福山
    "C2A540",  # 四堵

    # 畜試所各分所
    "B2U990",  # 畜試東區分所
    "B2Q810",  # 畜試南區分所
    "B2N890",  # 畜產試驗所
    "B2E890",  # 畜試北區分所

    # 其他農業相關單位
    "A2N290",  # 臺南蘭花園區
    "A2K630",  # 臺大雲林校區
    "A2K360",  # 水試臺西試驗場
    "A2C560",  # 農工中心

    # 茶改場各分場
    "82S580",  # 茶改東部分場
    "82H840",  # 茶改南部分場
    "82H320",  # 茶改中部分場
    "82C160",  # 茶改場
    "82A750",  # 茶改北部分場

    # 各農改場及分場
    "72V140",  # 高改旗南分場
    "72U480",  # 花改蘭陽分場
    "72T250",  # 花蓮農改場
    "72S590",  # 東改賓朗果園
    "72S200",  # 東改班鳩分場
    "72Q010",  # 高雄農改場
    "72N240",  # 七股研究中心
    "72N100",  # 臺南農改場
    "72M700",  # 南改鹿草分場
    "72M360",  # 南改義竹分場
    "72K220",  # 南改斗南分場
    "72HA00",  # 中改埔里分場
    "72G600",  # 臺中農改場
    "72D680",  # 桃改新埔分場
    "72D080",  # 桃改五峰分場
    "72C440",  # 桃園農改場
    "72AI40",  # 桃改樹林分場

    # 其他工作站
    "42HA10",  # 萬大發電廠
    "12Q980",  # 恆春工作站
    "12Q970",  # 東港工作站
    "12J990",  # 口湖工作站
]

def download_single_station_month(station_id, year_month, thread_id, timeout_seconds=180):
    """
    下載單一農業站的單一月份資料

    Args:
        station_id: 農業站代號
        year_month: 年月字串，格式如 "2023/07"
        thread_id: 執行緒ID
        timeout_seconds: 超時秒數

    Returns:
        dict: 下載結果
    """
    try:
        print(f"[執行緒 {thread_id}] 開始下載 {station_id} {year_month}")

        # 設定Chrome選項
        chrome_options = webdriver.ChromeOptions()
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-gpu")
        chrome_options.add_argument("--window-size=1920,1080")

        # 設定下載目錄
        download_dir = os.path.abspath("D:/Users/<USER>/Downloads")
        prefs = {
            "download.default_directory": download_dir,
            "download.prompt_for_download": False,
            "download.directory_upgrade": True,
            "safebrowsing.enabled": True
        }
        chrome_options.add_experimental_option("prefs", prefs)

        # 啟動瀏覽器
        driver = webdriver.Chrome(options=chrome_options)
        driver.set_page_load_timeout(30)

        try:
            # 訪問正確的網站
            driver.get("https://codis.cwa.gov.tw/StationData")
            time.sleep(3)

            wait = WebDriverWait(driver, 15)

            # 取消預設勾選的"署屬有人站"
            try:
                manned_station = wait.until(
                    EC.element_to_be_clickable((By.XPATH, "//div[@class='form-check' and .//input[@value='C1']]"))
                )
                # 檢查是否已勾選，如果是則取消勾選
                checkbox = manned_station.find_element(By.TAG_NAME, "input")
                if checkbox.is_selected():
                    manned_station.click()
                    print(f"[執行緒 {thread_id}] 已取消署屬有人站選項")
                time.sleep(1)
            except Exception as e:
                print(f"[執行緒 {thread_id}] 取消署屬有人站失敗: {e}")

            # 選擇農業站選項
            try:
                agricultural_station = wait.until(
                    EC.element_to_be_clickable((By.XPATH, "//div[@class='form-check' and .//input[@value='agr']]"))
                )
                agricultural_station.click()
                print(f"[執行緒 {thread_id}] 已選擇農業站選項")
                time.sleep(2)
            except Exception as e:
                print(f"[執行緒 {thread_id}] 選擇農業站失敗: {e}")
                return {"status": "error", "error": f"選擇農業站失敗: {e}"}

            # 輸入測站代號
            try:
                stn_input = wait.until(EC.presence_of_element_located((By.XPATH, "//input[@class='form-control']")))
                stn_input.clear()
                stn_input.send_keys(station_id)
                time.sleep(2)
                print(f"[執行緒 {thread_id}] 已輸入測站代號: {station_id}")
            except Exception as e:
                print(f"[執行緒 {thread_id}] 輸入測站代號失敗: {e}")
                return {"status": "error", "error": f"輸入測站代號失敗: {e}"}

            # 點擊地圖標記
            try:
                map_marker = wait.until(
                    EC.element_to_be_clickable((By.XPATH, "//img[contains(@src, 'red-dot.png')]"))
                )
                map_marker.click()
                print(f"[執行緒 {thread_id}] 已點擊地圖標記")
                time.sleep(2)
            except Exception as e:
                print(f"[執行緒 {thread_id}] 點擊地圖標記失敗: {e}")
                return {"status": "error", "error": f"點擊地圖標記失敗: {e}"}

            # 點擊資料圖表展示
            try:
                chart_button = wait.until(
                    EC.element_to_be_clickable((By.XPATH, "//input[@value='資料圖表展示']"))
                )
                chart_button.click()
                print(f"[執行緒 {thread_id}] 已點擊資料圖表展示")
                time.sleep(3)
            except Exception as e:
                print(f"[執行緒 {thread_id}] 點擊資料圖表展示失敗: {e}")
                return {"status": "error", "error": f"點擊資料圖表展示失敗: {e}"}

            # 選擇月報表
            try:
                month_radio = wait.until(
                    EC.element_to_be_clickable((By.ID, "month"))
                )
                month_radio.click()
                print(f"[執行緒 {thread_id}] 已選擇月報表")
                time.sleep(1)
            except Exception as e:
                print(f"[執行緒 {thread_id}] 選擇月報表失敗: {e}")
                return {"status": "error", "error": f"選擇月報表失敗: {e}"}

            # 設定年月
            try:
                year, month = year_month.split('/')

                # 設定年份
                year_select = Select(driver.find_element(By.ID, "year"))
                year_select.select_by_value(year)

                # 設定月份
                month_select = Select(driver.find_element(By.ID, "month_s"))
                month_select.select_by_value(month)

                print(f"[執行緒 {thread_id}] 已設定年月: {year_month}")
                time.sleep(1)
            except Exception as e:
                print(f"[執行緒 {thread_id}] 設定年月失敗: {e}")
                return {"status": "error", "error": f"設定年月失敗: {e}"}

            # 點擊送出按鈕
            try:
                submit_button = driver.find_element(By.XPATH, "//input[@value='送出']")
                submit_button.click()
                print(f"[執行緒 {thread_id}] 已點擊送出按鈕")

                # 等待下載完成
                start_time = time.time()
                while time.time() - start_time < timeout_seconds:
                    time.sleep(2)

                    # 檢查是否有下載的檔案
                    expected_filename = f"{station_id}-{year}-{month.zfill(2)}.csv"
                    download_path = Path(download_dir) / expected_filename

                    if download_path.exists():
                        print(f"[執行緒 {thread_id}] ✓ {station_id} {year_month} 下載完成")

                        # 移動檔案到目標目錄
                        target_dir = Path("data/raw/weather/agricultural_weather_station") / station_id
                        os.makedirs(target_dir, exist_ok=True)
                        target_path = target_dir / expected_filename

                        if not target_path.exists():
                            download_path.rename(target_path)
                            print(f"[執行緒 {thread_id}] 檔案已移動到: {target_path}")

                        return {"status": "success", "file": str(target_path)}

                # 超時
                print(f"[執行緒 {thread_id}] ⚠ {station_id} {year_month} 下載超時")
                return {"status": "timeout", "error": "下載超時"}

            except Exception as e:
                print(f"[執行緒 {thread_id}] 送出失敗: {e}")
                return {"status": "error", "error": f"送出失敗: {e}"}

        finally:
            driver.quit()

    except Exception as e:
        print(f"[執行緒 {thread_id}] {station_id} {year_month} 發生異常: {e}")
        return {"status": "error", "error": str(e)}

# 農業站總數統計
print(f"已載入 {len(AGRICULTURAL_STATIONS)} 個農業氣象站代號")

def cleanup_chrome_processes():
    """
    清理殘留的Selenium Chrome進程，不影響用戶正在使用的Chrome
    """
    try:
        import psutil
        killed_count = 0

        for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
            try:
                # 檢查是否為Chrome進程
                if proc.info['name'] and 'chrome' in proc.info['name'].lower():
                    # 檢查命令行參數，確保是由selenium啟動的
                    cmdline = proc.info['cmdline']
                    if cmdline:
                        # 只清理包含Selenium特有參數的Chrome進程
                        selenium_indicators = [
                            '--test-type',
                            '--disable-extensions',
                            '--no-sandbox',
                            '--disable-dev-shm-usage',
                            '--remote-debugging-port',
                            '--disable-gpu',
                            '--disable-web-security'
                        ]

                        # 檢查是否包含Selenium特有的參數
                        if any(indicator in ' '.join(cmdline) for indicator in selenium_indicators):
                            print(f"清理Selenium Chrome進程 PID: {proc.info['pid']}")
                            proc.terminate()
                            killed_count += 1

                # 總是清理chromedriver進程
                elif proc.info['name'] and 'chromedriver' in proc.info['name'].lower():
                    print(f"清理ChromeDriver進程 PID: {proc.info['pid']}")
                    proc.terminate()
                    killed_count += 1

            except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                pass

        if killed_count > 0:
            print(f"清理了 {killed_count} 個 Selenium Chrome/ChromeDriver 進程")
        else:
            print("沒有發現需要清理的Selenium進程")

        # 等待進程完全結束
        time.sleep(2)

    except ImportError:
        print("未安裝 psutil，跳過自動清理")
        print("請手動關閉不需要的Chrome視窗")

def download_agricultural_station(station_id, start_date="2023/07", end_date="2025/06", 
                                download_dir=None, timeout_per_month=90):
    """
    下載單一農業氣象站資料
    """
    driver = None
    start_time = time.time()
    max_total_time = 600  # 10分鐘總超時
    
    try:
        if download_dir is None:
            download_dir = Path("data/raw/weather/agricultural_weather_station") / station_id
        
        os.makedirs(download_dir, exist_ok=True)
        print(f"農業站 {station_id} 檔案將下載到: {download_dir}")

        # 設定 Chrome 選項
        chrome_options = webdriver.ChromeOptions()
        abs_download_dir = str(Path(download_dir).resolve())
        prefs = {
            "download.default_directory": abs_download_dir,
            "download.prompt_for_download": False,
            "download.directory_upgrade": True,
            "safebrowsing.enabled": True
        }
        chrome_options.add_experimental_option("prefs", prefs)
        
        # 基本設定
        chrome_options.add_argument("--disable-extensions")
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--window-size=1200,800")
        
        driver = webdriver.Chrome(options=chrome_options)
        wait = WebDriverWait(driver, 15)
        driver.set_page_load_timeout(30)

        # 日期處理
        start_dt = datetime.strptime(start_date, "%Y/%m")
        end_dt = datetime.strptime(end_date, "%Y/%m")

        # 檢查總超時
        if time.time() - start_time > max_total_time:
            raise TimeoutError(f"農業站 {station_id} 總執行時間超過 {max_total_time} 秒")

        driver.get("https://codis.cwa.gov.tw/StationData")
        time.sleep(3)

        # 取消預設勾選的"署屬有人站"
        try:
            manned_station = wait.until(
                EC.element_to_be_clickable((By.XPATH, "//div[@class='form-check' and .//input[@value='C1']]"))
            )
            # 檢查是否已勾選，如果是則取消勾選
            checkbox = manned_station.find_element(By.TAG_NAME, "input")
            if checkbox.is_selected():
                manned_station.click()
                print(f"農業站 {station_id}: 已取消署屬有人站選項")
            time.sleep(1)
        except Exception as e:
            print(f"農業站 {station_id}: 取消署屬有人站失敗: {e}")

        # 選擇農業站
        try:
            agricultural_station = wait.until(
                EC.element_to_be_clickable((By.XPATH, "//div[@class='form-check' and .//input[@value='agr']]"))
            )
            agricultural_station.click()
            print(f"農業站 {station_id}: 已選擇農業站選項")
            time.sleep(2)
        except Exception as e:
            raise Exception(f"無法選擇農業站選項: {e}")

        # 輸入農業站ID
        try:
            stn_input = wait.until(EC.presence_of_element_located((By.XPATH, "//input[@class='form-control']")))
            stn_input.clear()
            stn_input.send_keys(station_id)
            time.sleep(2)
            print(f"農業站 {station_id}: 已輸入測站代號")
        except Exception as e:
            raise Exception(f"無法輸入測站ID: {e}")

        # 點地圖標記
        try:
            wait.until(EC.element_to_be_clickable((By.XPATH, "//div[contains(@class, 'leaflet-interactive')]"))).click()
            time.sleep(2)
            print(f"農業站 {station_id}: 已點擊地圖標記")
        except Exception as e:
            raise Exception(f"無法點擊地圖標記: {e}")

        # 點擊 '資料圖表展示' 按鈕
        try:
            wait.until(EC.element_to_be_clickable((By.XPATH, f"//button[contains(@class, 'show_stn_tool') and contains(@data-stn_id, '{station_id}')]"))).click()
            time.sleep(2)
            print(f"農業站 {station_id}: 已點擊資料圖表展示")
        except Exception as e:
            raise Exception(f"無法點擊資料圖表展示按鈕: {e}")

        # 點月報表
        try:
            wait.until(EC.element_to_be_clickable((By.XPATH, "//*[@id='main_content']/section[2]/div/div/aside/div[2]/div[2]/div[2]/div"))).click()
            time.sleep(2)
            print(f"農業站 {station_id}: 已選擇月報表")
        except Exception as e:
            raise Exception(f"無法點擊月報表: {e}")

        # 下載各月份資料
        current_dt = start_dt
        downloaded_count = 0
        
        while current_dt <= end_dt:
            # 檢查總超時
            if time.time() - start_time > max_total_time:
                print(f"農業站 {station_id} 總執行時間超過 {max_total_time} 秒，強制停止")
                break
                
            month_start_time = time.time()
            year, month = current_dt.year, current_dt.month

            try:
                # 開啟日期選單
                datetime_panel = wait.until(
                    EC.element_to_be_clickable((By.XPATH, '//*[@id="main_content"]/section[2]/div/div/section/div[6]/div[1]/div[1]/label/div/div[2]/div[1]'))
                )
                datetime_panel.click()
                time.sleep(1)

                # 點擊年份
                year_selector = wait.until(EC.element_to_be_clickable((By.XPATH, "//div[contains(@class, 'vdatetime-popup__year')]")))
                year_selector.click()
                target_year = wait.until(EC.element_to_be_clickable((By.XPATH, f"//div[contains(@class, 'vdatetime-year-picker__item') and contains(text(), '{year}')]")))
                driver.execute_script("arguments[0].scrollIntoView({behavior: 'smooth', block: 'center'});", target_year)
                target_year.click()

                # 點擊月份
                target_month = wait.until(EC.element_to_be_clickable((By.XPATH, f"//div[contains(@class, 'vdatetime-month-picker__item') and contains(text(), '{month}月')]")))
                driver.execute_script("arguments[0].scrollIntoView({behavior: 'smooth', block: 'center'});", target_month)
                target_month.click()

                # 點擊下載
                download_btn = wait.until(EC.element_to_be_clickable((By.XPATH, "//*[@id='main_content']/section[2]/div/div/section/div[6]/div[1]/div[2]/div")))
                download_btn.click()
                print(f"農業站 {station_id}: 下載 {year}-{month:02d} 中...")
                
                # 等待下載完成
                download_wait_time = 0
                while download_wait_time < timeout_per_month:
                    time.sleep(1)
                    download_wait_time += 1
                    
                    if time.time() - month_start_time > timeout_per_month:
                        print(f"  農業站 {station_id}-{year}-{month:02d} 下載超時，跳過")
                        break
                        
                    if time.time() - start_time > max_total_time:
                        print(f"農業站 {station_id} 總執行時間超過限制，停止下載")
                        return
                
                downloaded_count += 1
                
            except Exception as e:
                print(f"  農業站 {station_id}-{year}-{month:02d} 下載失敗: {e}")
            
            # 到下個月
            next_month = (current_dt.month % 12) + 1
            next_year = current_dt.year + (current_dt.month // 12)
            current_dt = datetime(next_year, next_month, 1)

        print(f"農業站 {station_id} 下載完成，共處理 {downloaded_count} 個月")
        
    except Exception as e:
        print(f"農業站 {station_id} 下載過程中發生錯誤: {e}")
        
    finally:
        # 確保關閉瀏覽器
        if driver is not None:
            try:
                driver.quit()
                print(f"農業站 {station_id} 瀏覽器已關閉")
            except Exception as e:
                print(f"關閉瀏覽器時發生錯誤: {e}")
                
        # 檢查執行時間
        total_time = time.time() - start_time
        print(f"農業站 {station_id} 總執行時間: {total_time:.1f} 秒")

def download_single_agricultural_station(station_id, start_date, end_date, base_download_dir, thread_id):
    """
    單一農業站下載函數（用於多執行緒）
    """
    try:
        station_dir = base_download_dir / station_id
        print(f"[執行緒 {thread_id}] 正在下載農業站: {station_id}")

        # 下載該農業站的資料
        download_agricultural_station(
            station_id=station_id,
            start_date=start_date,
            end_date=end_date,
            download_dir=station_dir,
            timeout_per_month=90  # 每個月最多1.5分鐘
        )

        # 檢查預設下載目錄
        default_download_dir = Path("D:/Users/<USER>/Downloads")
        csv_files_default = list(default_download_dir.glob(f"{station_id}-*.csv"))

        # 移動檔案到目標目錄
        if csv_files_default:
            os.makedirs(station_dir, exist_ok=True)
            for file in csv_files_default:
                try:
                    target_file = station_dir / file.name
                    if not target_file.exists():
                        file.rename(target_file)
                        print(f"[執行緒 {thread_id}] 移動檔案: {file.name}")
                except Exception as e:
                    print(f"[執行緒 {thread_id}] 移動檔案失敗: {e}")

        # 統計結果
        final_csv_files = list(station_dir.glob("*.csv")) if station_dir.exists() else []
        total_files = len(final_csv_files)

        if total_files > 0:
            print(f"[執行緒 {thread_id}] 農業站 {station_id} 下載成功，共 {total_files} 個檔案")
            return {"station_id": station_id, "status": "success", "files": total_files}
        else:
            print(f"[執行緒 {thread_id}] 農業站 {station_id} 可能不存在或無資料")
            return {"station_id": station_id, "status": "no_data", "files": 0}

    except Exception as e:
        print(f"[執行緒 {thread_id}] 農業站 {station_id} 下載失敗: {e}")
        return {"station_id": station_id, "status": "error", "error": str(e)[:100]}

def parallel_download_agricultural_stations(start_date="2023/07", end_date="2025/06", max_workers=5):
    """
    並行下載所有農業氣象站資料
    """
    print("=" * 60)
    print("農業氣象站批量下載程式")
    print("=" * 60)
    print(f"下載時間範圍: {start_date} 到 {end_date}")
    print(f"並行執行緒數: {max_workers}")
    print(f"農業站總數: {len(AGRICULTURAL_STATIONS)}")
    print()

    # 清理殘留進程
    cleanup_chrome_processes()

    # 設定下載目錄
    base_download_dir = Path("data/raw/weather/agricultural_weather_station")
    os.makedirs(base_download_dir, exist_ok=True)

    results = []
    completed_count = 0
    total_stations = len(AGRICULTURAL_STATIONS)

    start_time = time.time()
    max_total_time = 7200  # 2小時總超時

    try:
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交下載任務
            future_to_station = {
                executor.submit(
                    download_single_agricultural_station,
                    station_id,
                    start_date,
                    end_date,
                    base_download_dir,
                    i % max_workers + 1
                ): station_id
                for i, station_id in enumerate(AGRICULTURAL_STATIONS)
            }

            # 處理完成的任務
            try:
                for future in as_completed(future_to_station, timeout=max_total_time):
                    station_id = future_to_station[future]
                    completed_count += 1

                    try:
                        result = future.result(timeout=600)  # 單一農業站10分鐘超時
                        results.append(result)

                        # 顯示進度
                        progress = (completed_count / total_stations) * 100
                        elapsed = (time.time() - start_time) / 60
                        print(f"進度: {completed_count}/{total_stations} ({progress:.1f}%) - 已用時 {elapsed:.1f} 分鐘")

                    except TimeoutError:
                        print(f"農業站 {station_id} 執行超時，跳過")
                        results.append({"station_id": station_id, "status": "timeout", "error": "執行超時"})

                    except Exception as e:
                        print(f"農業站 {station_id} 處理時發生錯誤: {e}")
                        results.append({"station_id": station_id, "status": "error", "error": str(e)})

            except TimeoutError:
                print("\n\n整體下載超時，正在停止所有任務...")
                for future in future_to_station:
                    future.cancel()

            except KeyboardInterrupt:
                print("\n\n用戶中斷下載，正在停止所有任務...")
                for future in future_to_station:
                    future.cancel()
                raise

    except Exception as e:
        print(f"並行下載過程中發生錯誤: {e}")

    # 統計結果
    success_count = sum(1 for r in results if r.get("status") == "success")
    no_data_count = sum(1 for r in results if r.get("status") == "no_data")
    error_count = sum(1 for r in results if r.get("status") == "error")
    timeout_count = sum(1 for r in results if r.get("status") == "timeout")
    total_files = sum(r.get("files", 0) for r in results if r.get("status") == "success")

    print(f"\n=== 農業氣象站下載完成摘要 ===")
    print(f"處理農業站數: {completed_count}/{total_stations}")
    print(f"成功農業站數: {success_count}")
    print(f"無資料農業站數: {no_data_count}")
    print(f"錯誤農業站數: {error_count}")
    print(f"超時農業站數: {timeout_count}")
    print(f"總下載檔案數: {total_files}")
    print(f"總執行時間: {(time.time() - start_time)/60:.1f} 分鐘")

    # 清理殘留進程
    cleanup_chrome_processes()

    return results

def test_agricultural_stations(test_stations=None, max_workers=2):
    """
    測試模式：只下載少數幾個農業站來驗證程式是否正常工作
    """
    global AGRICULTURAL_STATIONS

    if test_stations is None:
        # 選擇前5個農業站進行測試
        test_stations = AGRICULTURAL_STATIONS[:5]

    print("=" * 60)
    print("農業氣象站測試模式")
    print("=" * 60)
    print(f"測試農業站: {test_stations}")
    print(f"並行執行緒數: {max_workers}")
    print()

    # 暫時替換全域清單
    original_stations = AGRICULTURAL_STATIONS.copy()
    AGRICULTURAL_STATIONS = test_stations

    try:
        results = parallel_download_agricultural_stations(
            start_date="2023/07",
            end_date="2023/08",  # 只下載2個月進行測試
            max_workers=max_workers
        )
        return results
    finally:
        # 恢復原始清單
        AGRICULTURAL_STATIONS = original_stations

def main():
    """主函數"""
    print("農業氣象站資料下載工具")
    print("下載時間範圍: 2023年7月 到 2025年6月")
    print("=" * 50)

    # 選擇模式
    print("請選擇運行模式:")
    print("1. 測試模式（下載前5個農業站的2個月資料）")
    print("2. 完整模式（下載所有農業站的完整資料）")

    while True:
        try:
            mode = int(input("請選擇模式 (1 或 2): "))
            if mode in [1, 2]:
                break
            else:
                print("請輸入1或2")
        except ValueError:
            print("請輸入有效的數字")

    # 讓用戶選擇並行視窗數
    max_limit = 3 if mode == 1 else 12
    while True:
        try:
            max_workers = int(input(f"請選擇同時運行的視窗數量 (1-{max_limit}): "))
            if 1 <= max_workers <= max_limit:
                break
            else:
                print(f"請輸入1到{max_limit}之間的數字")
        except ValueError:
            print("請輸入有效的數字")

    if mode == 1:
        print(f"\n測試模式：將使用 {max_workers} 個並行視窗下載前5個農業站的2個月資料")
        print("這有助於驗證農業站代號是否正確")
    else:
        print(f"\n完整模式：將使用 {max_workers} 個並行視窗下載所有農業氣象站資料")
        print("注意：請確保農業站代號清單已根據農業站.jpg圖片更新")

    print("按 Enter 開始下載，或按 Ctrl+C 取消...")
    input()

    try:
        if mode == 1:
            test_agricultural_stations(max_workers=max_workers)
        else:
            parallel_download_agricultural_stations(
                start_date="2023/07",
                end_date="2025/06",
                max_workers=max_workers
            )
        print("農業氣象站下載程式正常結束")

    except KeyboardInterrupt:
        print("\n程式被用戶中斷")
    except Exception as e:
        print(f"\n程式執行錯誤: {e}")
    finally:
        print("清理殘留進程...")
        cleanup_chrome_processes()
        print("程式結束")

if __name__ == "__main__":
    main()
