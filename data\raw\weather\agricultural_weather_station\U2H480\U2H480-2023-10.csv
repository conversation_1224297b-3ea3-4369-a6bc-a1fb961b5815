﻿"觀測時間(day)","測站氣壓(hPa)","重力位高度(gpm)","測站最高氣壓(hPa)","測站最高氣壓時間(LST)","測站最低氣壓(hPa)","測站最低氣壓時間(LST)","氣溫(℃)","最高氣溫(℃)","最高氣溫時間(LST)","最低氣溫(℃)","最低氣溫時間(LST)","露點溫度(℃)","相對溼度(%)","最小相對溼度(%)","最小相對溼度時間(LST)","風速(m/s)","風向(360degree)","最大瞬間風(m/s)","最大瞬間風風向(360degree)","最大瞬間風風速時間(LST)","降水量(mm)","降水時數(hour)","最大十分鐘降水量(mm)","最大十分鐘降水量起始時間(LST)","日照時數(hour)","日照率(%)","全天空日射量(MJ/㎡)","A型蒸發量(mm)","地溫0cm","地溫5cm","地溫10cm","地溫20cm","地溫30cm","地溫50cm","地溫100cm"
"ObsTime","StnPres","Geopotential Height","StnPresMax","StnPresMaxTime","StnPresMin","StnPresMinTime","Temperature","T Max","T Max Time","T Min","T Min Time","Td dew point","RH","RHMin","RHMinTime","WS","WD","WSGust","WDGust","WGustTime","Precp","PrecpHour","PrecpMax10","PrecpMax10Time","SunShine","SunshineRate","GloblRad","EvapA","TxSoil0cm","TxSoil5cm","TxSoil10cm","TxSoil20cm","TxSoil30cm","TxSoil50cm","TxSoil100cm"
"01","883.4","1011.9","884.6","2023/10/01 09:17:00","882.0","2023/10/01 16:33:00","21.7","25.9","2023/10/01 13:20:00","18.5","2023/10/01 05:54:00","19.9","89","72","2023/10/01 13:16:00","0.6","200","4.0","360","2023/10/01 12:07:00","0.0","X","0.0","2023/10/01 00:01:00","8.7","X","10.74","--","21.4","21.5","21.5","21.4","/","21.2","21.0"
"02","883.9","1012.4","885.4","2023/10/02 08:54:00","882.5","2023/10/02 15:47:00","21.7","27.4","2023/10/02 13:11:00","19.1","2023/10/02 06:07:00","20.1","90","69","2023/10/02 13:11:00","0.6","180","4.6","330","2023/10/02 12:18:00","0.0","X","0.0","2023/10/02 00:01:00","7.8","X","9.42","--","21.7","21.8","21.7","21.6","/","21.4","21.0"
"03","881.4","1009.6","882.7","2023/10/03 08:00:00","880.0","2023/10/03 16:00:00","20.0","23.3","2023/10/03 10:00:00","18.6","2023/10/03 22:00:00","18.9","93","80","2023/10/03 10:00:00","0.4","190","2.8","180","2023/10/03 10:00:00","0.0","X","0.0","2023/10/03 00:00:00","1.3","X","2.82","X","20.5","21.3","21.5","21.6","/","21.5","21.1"
"04","877.4","1005.5","881.4","2023/10/04 00:13:00","874.8","2023/10/04 23:51:00","20.6","24.7","2023/10/04 13:34:00","18.2","2023/10/04 04:28:00","19.7","94","78","2023/10/04 13:35:00","0.6","330","4.3","40","2023/10/04 22:54:00","0.5","X","0.5","2023/10/04 22:30:00","4.8","X","6.40","--","20.8","21.1","21.1","21.2","/","21.3","21.0"
"05","874.8","1001.9","881.2","2023/10/05 23:44:00","871.5","2023/10/05 03:58:00","21.9","26.5","2023/10/05 13:45:00","19.7","2023/10/05 05:54:00","20.5","91","76","2023/10/05 13:43:00","0.8","120","6.4","180","2023/10/05 08:19:00","0.0","X","0.0","2023/10/05 00:01:00","7.1","X","6.65","--","21.4","21.5","21.5","21.4","/","21.3","21.0"
"06","883.2","1012.1","884.9","2023/10/06 22:27:00","880.7","2023/10/06 01:00:00","20.7","23.7","2023/10/06 14:24:00","19.4","2023/10/06 23:54:00","19.7","94","79","2023/10/06 14:14:00","0.3","320","2.8","330","2023/10/06 16:56:00","26.5","X","7.0","2023/10/06 15:49:00","4.5","X","4.38","--","20.9","21.3","21.3","21.4","/","21.4","21.0"
"07","885.0","1014.4","886.8","2023/10/07 21:10:00","883.4","2023/10/07 03:52:00","20.2","23.3","2023/10/07 09:38:00","18.3","2023/10/07 05:54:00","19.2","94","76","2023/10/07 09:42:00","0.5","220","3.4","10","2023/10/07 18:14:00","39.5","X","8.0","2023/10/07 18:36:00","5.2","X","5.66","--","20.4","20.8","20.9","21.0","/","21.2","20.9"
"08","887.0","1016.7","888.7","2023/10/08 21:38:00","885.4","2023/10/08 03:58:00","20.2","24.5","2023/10/08 11:45:00","18.3","2023/10/08 23:55:00","19.1","94","77","2023/10/08 11:44:00","0.7","190","4.5","340","2023/10/08 15:34:00","61.0","X","15.5","2023/10/08 15:59:00","5.8","X","6.86","--","20.1","20.4","20.5","20.6","/","20.8","20.4"
"09","887.8","1017.6","889.5","2023/10/09 20:34:00","886.5","2023/10/09 04:17:00","20.3","24.7","2023/10/09 10:24:00","17.6","2023/10/09 04:54:00","19.3","94","76","2023/10/09 09:57:00","0.8","190","3.4","350","2023/10/09 10:26:00","1.0","X","0.5","2023/10/09 05:42:00","5.0","X","10.62","--","20.3","20.5","20.6","20.6","/","20.7","20.5"
"10","887.8","1017.4","889.2","2023/10/10 07:37:00","886.2","2023/10/10 14:50:00","20.7","24.7","2023/10/10 10:42:00","18.3","2023/10/10 06:19:00","19.5","93","78","2023/10/10 10:43:00","0.5","230","4.3","310","2023/10/10 12:01:00","0.5","X","0.5","2023/10/10 01:04:00","6.2","X","9.70","--","20.8","21.0","20.9","20.9","/","20.8","20.7"
"11","888.3","1018.1","889.5","2023/10/11 21:31:00","887.3","2023/10/11 14:55:00","20.4","23.0","2023/10/11 09:02:00","18.7","2023/10/11 21:19:00","19.3","93","80","2023/10/11 09:02:00","0.3","240","2.6","10","2023/10/11 10:42:00","3.0","X","0.5","2023/10/11 00:36:00","4.1","X","5.38","--","20.6","21.0","21.0","21.0","/","20.9","20.7"
"12","888.5","1018.9","889.8","2023/10/12 10:00:00","887.4","2023/10/12 15:00:00","19.3","24.3","2023/10/12 10:00:00","16.4","2023/10/12 23:59:00","18.4","95","82","2023/10/12 10:00:00","0.7","190","4.0","330","2023/10/12 13:00:00","0.0","X","0.0","2023/10/12 00:01:00","5.9","X","7.73","--","19.9","20.5","20.6","20.8","/","20.9","20.8"
"13","887.0","1017.5","888.7","2023/10/13 08:52:00","885.5","2023/10/13 15:03:00","18.5","21.8","2023/10/13 10:26:00","16.4","2023/10/13 00:03:00","18.2","97","83","2023/10/13 10:20:00","0.5","150","3.9","320","2023/10/13 12:05:00","16.5","X","3.0","2023/10/13 17:38:00","4.2","X","4.59","--","19.4","19.9","20.1","20.4","/","20.7","20.8"
"14","885.6","1016.2","886.9","2023/10/14 09:05:00","884.2","2023/10/14 14:52:00","17.9","20.5","2023/10/14 09:43:00","15.7","2023/10/14 05:54:00","17.7","98","86","2023/10/14 09:50:00","0.5","200","2.8","350","2023/10/14 10:11:00","0.5","X","0.5","2023/10/14 01:00:00","4.1","X","4.78","--","19.1","19.7","19.8","20.1","/","20.5","20.7"
"15","886.1","1016.7","887.6","2023/10/15 23:12:00","884.7","2023/10/15 04:07:00","18.1","22.7","2023/10/15 10:11:00","15.9","2023/10/15 05:27:00","17.3","95","70","2023/10/15 08:47:00","0.7","200","4.4","350","2023/10/15 10:31:00","0.0","X","0.0","2023/10/15 00:01:00","5.8","X","9.83","--","19.4","19.9","20.0","20.1","/","20.4","20.6"
"16","887.6","1018.3","889.0","2023/10/16 23:32:00","886.5","2023/10/16 03:06:00","18.2","22.1","2023/10/16 11:44:00","15.6","2023/10/16 03:54:00","17.2","94","73","2023/10/16 09:54:00","0.6","180","2.9","350","2023/10/16 13:13:00","0.0","X","0.0","2023/10/16 00:01:00","6.5","X","7.24","--","19.3","19.8","19.9","20.1","/","20.4","20.6"
"17","888.8","1019.7","890.2","2023/10/17 09:47:00","887.2","2023/10/17 04:00:00","18.1","21.3","2023/10/17 10:33:00","16.1","2023/10/17 23:42:00","17.3","94","76","2023/10/17 10:34:00","0.7","180","2.8","350","2023/10/17 11:08:00","0.5","X","0.5","2023/10/17 07:47:00","7.1","X","5.83","--","19.2","19.8","19.9","20.1","/","20.3","20.5"
"18","889.5","1020.4","890.9","2023/10/18 09:29:00","888.3","2023/10/18 15:34:00","18.4","23.5","2023/10/18 08:43:00","15.7","2023/10/18 20:16:00","17.2","92","62","2023/10/18 09:00:00","0.8","210","2.9","340","2023/10/18 13:55:00","0.0","X","0.0","2023/10/18 00:01:00","7.5","X","7.85","--","19.5","19.8","19.9","20.0","/","20.3","20.5"
"19","888.5","1019.1","889.8","2023/10/19 00:01:00","887.0","2023/10/19 14:52:00","18.7","24.4","2023/10/19 12:02:00","15.0","2023/10/19 05:30:00","17.3","92","61","2023/10/19 11:52:00","0.8","210","4.8","340","2023/10/19 12:36:00","0.0","X","0.0","2023/10/19 00:01:00","6.4","X","14.42","--","19.8","20.0","20.0","20.0","/","20.2","20.4"
"20","888.1","1018.6","889.6","2023/10/20 09:58:00","887.0","2023/10/20 04:08:00","18.8","22.6","2023/10/20 12:15:00","16.2","2023/10/20 06:19:00","17.7","94","76","2023/10/20 12:15:00","0.7","190","3.8","360","2023/10/20 13:15:00","0.0","X","0.0","2023/10/20 00:01:00","7.1","X","9.37","--","19.8","20.2","20.2","20.3","/","20.3","20.4"
"21","889.2","1020.1","890.7","2023/10/21 20:26:00","887.6","2023/10/21 04:26:00","18.3","21.5","2023/10/21 11:30:00","16.4","2023/10/21 02:07:00","17.8","97","82","2023/10/21 10:53:00","0.7","200","3.2","320","2023/10/21 11:09:00","0.0","X","0.0","2023/10/21 00:01:00","5.3","X","5.68","--","19.3","19.8","19.9","20.1","/","20.3","20.4"
"22","890.0","1021.1","891.3","2023/10/22 09:47:00","888.8","2023/10/22 03:43:00","18.2","23.6","2023/10/22 11:54:00","15.3","2023/10/22 22:54:00","17.3","94","74","2023/10/22 09:57:00","0.9","190","4.8","330","2023/10/22 12:45:00","0.0","X","0.0","2023/10/22 00:01:00","5.7","X","9.72","--","19.3","19.8","19.9","20.0","/","20.2","20.4"
"23","889.7","1020.7","891.0","2023/10/23 09:01:00","888.5","2023/10/23 14:30:00","18.4","23.7","2023/10/23 11:05:00","15.2","2023/10/23 05:25:00","17.4","94","69","2023/10/23 09:38:00","0.8","200","5.5","330","2023/10/23 11:13:00","2.0","X","1.5","2023/10/23 15:51:00","6.5","X","11.99","--","19.3","19.6","19.7","19.8","/","20.1","20.3"
"24","889.1","1019.8","891.3","2023/10/24 09:00:00","887.9","2023/10/24 15:28:00","18.9","24.9","2023/10/24 13:50:00","15.4","2023/10/24 05:05:00","17.4","91","63","2023/10/24 13:52:00","0.9","180","4.6","320","2023/10/24 12:45:00","0.0","X","0.0","2023/10/24 00:01:00","8.8","X","12.20","--","19.6","19.9","20.0","20.0","/","20.1","20.3"
"25","888.0","1018.8","889.2","2023/10/25 09:06:00","886.8","2023/10/25 14:30:00","18.2","24.1","2023/10/25 13:37:00","13.5","2023/10/25 23:37:00","16.4","90","56","2023/10/25 09:42:00","1.0","190","4.7","350","2023/10/25 11:55:00","0.0","X","0.0","2023/10/25 00:01:00","7.3","X","15.46","--","19.2","19.7","19.9","20.0","/","20.2","20.2"
"26","887.4","1018.7","888.5","2023/10/26 08:54:00","886.5","2023/10/26 03:49:00","16.9","23.2","2023/10/26 12:26:00","13.4","2023/10/26 06:05:00","15.2","90","60","2023/10/26 09:34:00","1.1","200","5.4","200","2023/10/26 00:21:00","0.0","X","0.0","2023/10/26 00:01:00","6.8","X","14.11","--","18.5","19.2","19.4","19.7","/","20.1","20.2"
"27","886.8","1017.7","888.3","2023/10/27 09:35:00","885.7","2023/10/27 14:48:00","17.5","21.5","2023/10/27 10:47:00","14.3","2023/10/27 00:27:00","16.6","94","70","2023/10/27 08:58:00","0.7","180","4.6","340","2023/10/27 09:03:00","10.0","X","2.5","2023/10/27 14:54:00","4.6","X","5.40","--","18.5","19.1","19.3","19.5","/","20.0","20.2"
"28","886.9","1017.8","888.2","2023/10/28 10:16:00","885.8","2023/10/28 04:36:00","17.5","20.8","2023/10/28 09:51:00","15.8","2023/10/29 00:00:00","17.4","99","87","2023/10/28 09:39:00","0.5","250","3.3","340","2023/10/28 11:28:00","4.0","X","2.0","2023/10/28 14:38:00","3.5","X","4.72","--","18.7","19.3","19.3","19.4","/","19.8","20.1"
"29","887.7","1019.2","889.2","2023/10/29 22:02:00","886.4","2023/10/29 15:07:00","16.8","20.1","2023/10/29 10:09:00","14.7","2023/10/29 05:08:00","16.5","98","82","2023/10/29 10:00:00","0.7","200","3.2","350","2023/10/29 13:45:00","0.0","X","0.0","2023/10/29 00:01:00","4.1","X","4.83","--","18.1","18.8","19.0","19.2","/","19.7","20.1"
"30","888.5","1020.1","889.8","2023/10/30 22:07:00","887.3","2023/10/30 03:41:00","16.8","19.9","2023/10/30 10:12:00","14.3","2023/10/30 05:08:00","16.2","96","81","2023/10/30 09:35:00","0.5","220","3.3","260","2023/10/30 10:37:00","0.0","X","0.0","2023/10/30 00:01:00","5.5","X","5.77","--","18.0","18.7","18.8","19.0","/","19.5","20.0"
"31","889.5","1020.9","890.5","2023/10/31 20:58:00","888.3","2023/10/31 14:48:00","17.2","22.4","2023/10/31 11:21:00","14.1","2023/10/31 05:54:00","16.0","92","68","2023/10/31 10:25:00","0.8","190","3.9","340","2023/10/31 10:51:00","0.0","X","0.0","2023/10/31 00:01:00","7.0","X","12.41","--","18.3","18.7","18.8","18.9","/","19.4","19.9"