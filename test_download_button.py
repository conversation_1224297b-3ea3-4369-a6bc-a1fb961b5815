"""
專門測試下載按鈕的點擊功能
"""

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
import time
from pathlib import Path
import os

def test_download_button():
    """
    測試下載按鈕的點擊功能
    """
    print("測試下載按鈕點擊功能...")
    
    # 設定下載目錄
    download_dir = Path("data/raw/weather/button_test").resolve()
    os.makedirs(download_dir, exist_ok=True)
    
    # 設定 Chrome 選項
    chrome_options = webdriver.ChromeOptions()
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--disable-gpu")
    chrome_options.add_argument("--disable-logging")
    chrome_options.add_argument("--disable-extensions")
    # 暫時不使用無頭模式以便觀察
    # chrome_options.add_argument("--headless=new")
    
    prefs = {
        "download.default_directory": str(download_dir),
        "download.prompt_for_download": False,
        "download.directory_upgrade": True,
        "safebrowsing.enabled": True,
    }
    chrome_options.add_experimental_option("prefs", prefs)
    
    service = Service(ChromeDriverManager().install())
    driver = webdriver.Chrome(service=service, options=chrome_options)
    wait = WebDriverWait(driver, 60)
    
    try:
        # 開啟 CODiS 網站
        print("正在開啟 CODiS 網站...")
        driver.get("https://codis.cwa.gov.tw/StationData")
        time.sleep(5)
        
        # 選擇自動氣象站
        print("選擇自動氣象站...")
        stn_type = wait.until(
            EC.element_to_be_clickable(
                (By.XPATH, "//div[@class='form-check' and .//input[@value='auto_C0']]")
            )
        )
        stn_type.click()
        time.sleep(3)
        
        # 選擇測站 C0A980
        print("選擇測站 C0A980...")
        station_select = wait.until(
            EC.element_to_be_clickable((By.ID, "station"))
        )
        station_select.click()
        time.sleep(2)
        
        station_option = wait.until(
            EC.element_to_be_clickable(
                (By.XPATH, "//option[@value='C0A980']")
            )
        )
        station_option.click()
        time.sleep(2)
        
        # 選擇月報表
        print("選擇月報表...")
        monthly_report = wait.until(
            EC.element_to_be_clickable(
                (By.XPATH, "//div[@class='form-check' and .//input[@value='MON']]")
            )
        )
        monthly_report.click()
        time.sleep(2)
        
        # 選擇年份 2024
        print("選擇年份 2024...")
        year_select = wait.until(
            EC.element_to_be_clickable((By.ID, "year"))
        )
        year_select.click()
        time.sleep(1)
        
        year_option = wait.until(
            EC.element_to_be_clickable(
                (By.XPATH, "//option[@value='2024']")
            )
        )
        year_option.click()
        time.sleep(2)
        
        # 選擇月份 1
        print("選擇月份 1...")
        month_option = wait.until(
            EC.element_to_be_clickable(
                (By.XPATH, "//option[@value='1']")
            )
        )
        month_option.click()
        time.sleep(2)
        
        # 測試不同的下載按鈕選擇器
        print("嘗試點擊下載按鈕...")
        download_success = False
        
        selectors = [
            "//img[@src='https://codis.cwa.gov.tw/Images/stn-tool/download.svg']",
            "//img[contains(@src, 'download.svg')]",
            "//img[contains(@src, 'download.svg')]/parent::*",
            "//img[contains(@src, 'download.svg')]/ancestor::div[1]",
            "//*[contains(@class, 'download')]",
            "//div[contains(@onclick, 'download')]",
            "//button[contains(@class, 'download')]"
        ]
        
        for i, selector in enumerate(selectors):
            try:
                print(f"嘗試選擇器 {i+1}: {selector}")
                download_btn = driver.find_element(By.XPATH, selector)
                print(f"  找到元素: {download_btn.tag_name}")
                print(f"  元素可見: {download_btn.is_displayed()}")
                print(f"  元素可點擊: {download_btn.is_enabled()}")
                
                if download_btn.is_displayed() and download_btn.is_enabled():
                    # 滾動到元素位置
                    driver.execute_script("arguments[0].scrollIntoView({behavior: 'smooth', block: 'center'});", download_btn)
                    time.sleep(1)
                    
                    # 嘗試點擊
                    download_btn.click()
                    print(f"  成功點擊下載按鈕！")
                    download_success = True
                    break
                    
            except Exception as e:
                print(f"  選擇器 {i+1} 失敗: {e}")
                continue
        
        if download_success:
            print("下載按鈕點擊成功，等待檔案下載...")
            time.sleep(15)
            
            # 檢查下載結果
            csv_files = list(download_dir.glob("*.csv"))
            if csv_files:
                print(f"成功下載 {len(csv_files)} 個檔案:")
                for file in csv_files:
                    size_kb = file.stat().st_size / 1024
                    print(f"  - {file.name} ({size_kb:.1f} KB)")
            else:
                print("未發現下載的檔案")
        else:
            print("無法找到或點擊下載按鈕")
        
        # 暫停讓用戶觀察
        print("程式將暫停30秒，請觀察瀏覽器...")
        time.sleep(30)
        
    except Exception as e:
        print(f"測試過程中發生錯誤: {e}")
        import traceback
        traceback.print_exc()
        
    finally:
        driver.quit()

if __name__ == "__main__":
    test_download_button()
