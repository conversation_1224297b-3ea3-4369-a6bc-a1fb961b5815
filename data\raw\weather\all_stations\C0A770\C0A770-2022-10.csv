﻿"觀測時間(day)","測站氣壓(hPa)","測站最高氣壓(hPa)","測站最高氣壓時間(LST)","測站最低氣壓(hPa)","測站最低氣壓時間(LST)","氣溫(℃)","最高氣溫(℃)","最高氣溫時間(LST)","最低氣溫(℃)","最低氣溫時間(LST)","相對溼度(%)","最小相對溼度(%)","最小相對溼度時間(LST)","風速(m/s)","風向(360degree)","最大瞬間風(m/s)","最大瞬間風風向(360degree)","最大瞬間風風速時間(LST)","降水量(mm)"
"ObsTime","StnPres","StnPresMax","StnPresMaxTime","StnPresMin","StnPresMinTime","Temperature","T Max","T Max Time","<PERSON> <PERSON>","T Min Time","RH","RHMin","RHMinTime","WS","WD","WSGust","WDGust","WGustTime","Precp"
"01","1008.1","1009.5","2022/10/01 07:46:00","1006.8","2022/10/01 15:06:00","30.0","36.1","2022/10/01 11:12:00","26.2","2022/10/01 05:09:00","59","40","2022/10/01 11:53:00","2.0","108","8.9","73","2022/10/01 13:55:00","0.0"
"02","1007.9","1009.5","2022/10/02 08:16:00","1006.1","2022/10/02 14:36:00","30.3","36.3","2022/10/02 10:40:00","25.3","2022/10/02 05:39:00","61","40","2022/10/02 10:40:00","2.1","85","10.0","115","2022/10/02 16:09:00","0.0"
"03","1007.6","1009.0","2022/10/03 08:36:00","1006.0","2022/10/03 14:06:00","30.7","37.5","2022/10/03 12:19:00","27.4","2022/10/03 06:18:00","60","41","2022/10/03 12:08:00","2.2","85","8.8","91","2022/10/03 01:04:00","0.0"
"04","1008.2","1010.2","2022/10/04 21:40:00","1005.7","2022/10/04 15:21:00","29.7","36.6","2022/10/04 11:56:00","25.4","2022/10/04 05:50:00","65","43","2022/10/04 11:52:00","1.9","80","9.0","119","2022/10/04 22:30:00","0.0"
"05","1010.4","1012.2","2022/10/05 09:54:00","1008.8","2022/10/05 15:25:00","27.6","31.4","2022/10/05 10:44:00","25.5","2022/10/05 23:19:00","74","58","2022/10/05 10:45:00","3.1","73","11.7","62","2022/10/05 12:57:00","0.5"
"06","1010.5","1012.1","2022/10/06 08:23:00","1008.6","2022/10/06 15:07:00","26.4","29.2","2022/10/06 13:33:00","24.7","2022/10/06 06:13:00","76","65","2022/10/06 13:33:00","3.7","74","11.1","89","2022/10/06 11:06:00","0.0"
"07","1010.4","1012.4","2022/10/07 21:17:00","1008.4","2022/10/07 14:51:00","25.8","27.7","2022/10/07 08:37:00","24.3","2022/10/07 13:37:00","79","70","2022/10/07 15:01:00","2.3","99","9.3","106","2022/10/07 21:12:00","14.0"
"08","1011.4","1012.6","2022/10/08 21:51:00","1010.1","2022/10/08 14:20:00","24.5","26.8","2022/10/08 11:34:00","23.2","2022/10/08 19:11:00","76","64","2022/10/08 11:30:00","2.5","95","9.1","74","2022/10/08 20:27:00","0.5"
"09","1011.6","1014.3","2022/10/09 23:50:00","1009.4","2022/10/09 14:41:00","24.8","28.4","2022/10/09 12:21:00","22.5","2022/10/09 20:54:00","77","65","2022/10/09 12:21:00","2.1","89","10.2","26","2022/10/10 00:00:00","3.5"
"10","1014.6","1016.2","2022/10/10 10:07:00","1013.1","2022/10/10 03:10:00","21.7","23.4","2022/10/10 00:01:00","20.7","2022/10/10 14:42:00","65","56","2022/10/10 14:13:00","3.2","55","12.0","69","2022/10/10 11:33:00","0.0"
"11","1013.5","1015.5","2022/10/11 08:47:00","1012.1","2022/10/11 23:30:00","21.0","22.2","2022/10/11 13:20:00","19.7","2022/10/11 17:54:00","65","54","2022/10/11 04:48:00","3.1","84","12.5","79","2022/10/11 02:36:00","0.0"
"12","1011.5","1012.9","2022/10/12 10:01:00","1010.3","2022/10/12 14:39:00","22.6","25.8","2022/10/12 11:45:00","20.8","2022/10/12 06:17:00","75","62","2022/10/12 00:30:00","3.3","87","13.2","91","2022/10/12 13:09:00","1.0"
"13","1009.5","1011.2","2022/10/13 00:01:00","1007.6","2022/10/13 15:08:00","23.8","27.0","2022/10/13 12:14:00","22.0","2022/10/13 00:55:00","75","61","2022/10/13 07:09:00","2.6","69","12.0","91","2022/10/13 06:13:00","0.0"
"14","1006.9","1008.9","2022/10/14 00:04:00","1005.1","2022/10/14 23:57:00","24.6","26.8","2022/10/14 10:09:00","22.5","2022/10/14 00:25:00","78","64","2022/10/14 11:12:00","3.2","54","14.8","63","2022/10/14 20:50:00","1.0"
"15","1004.2","1005.7","2022/10/15 08:42:00","1002.9","2022/10/15 12:47:00","25.1","26.7","2022/10/15 12:35:00","23.7","2022/10/15 04:05:00","85","76","2022/10/15 15:16:00","2.4","61","14.0","41","2022/10/15 15:20:00","11.5"
"16","1002.9","1004.4","2022/10/16 21:42:00","1001.2","2022/10/16 02:13:00","26.3","27.7","2022/10/16 09:46:00","24.9","2022/10/16 18:56:00","87","79","2022/10/16 02:13:00","3.9","61","16.6","84","2022/10/16 06:28:00","188.0"
"17","1007.3","1012.3","2022/10/17 22:53:00","1003.1","2022/10/17 02:06:00","23.8","26.3","2022/10/17 00:57:00","20.2","2022/10/17 23:50:00","77","70","2022/10/17 14:53:00","3.5","65","16.2","81","2022/10/17 04:01:00","6.0"
"18","1013.4","1015.1","2022/10/18 20:08:00","1011.8","2022/10/18 01:35:00","19.5","20.6","2022/10/18 09:56:00","18.5","2022/10/18 05:23:00","79","69","2022/10/18 19:17:00","2.3","56","10.4","61","2022/10/18 02:11:00","0.5"
"19","1014.5","1016.1","2022/10/19 20:18:00","1012.9","2022/10/19 04:16:00","21.3","22.7","2022/10/19 14:21:00","19.2","2022/10/19 00:01:00","65","59","2022/10/19 15:29:00","3.7","66","12.0","55","2022/10/19 12:42:00","0.0"
"20","1014.1","1015.3","2022/10/20 09:08:00","1013.2","2022/10/20 13:32:00","23.1","25.0","2022/10/20 12:15:00","21.4","2022/10/20 00:55:00","67","59","2022/10/20 11:13:00","3.5","65","12.5","67","2022/10/20 09:42:00","0.0"
"21","1011.3","1013.8","2022/10/21 08:38:00","1009.5","2022/10/21 15:09:00","25.3","29.0","2022/10/21 13:20:00","22.9","2022/10/21 06:01:00","72","56","2022/10/21 09:36:00","3.5","90","12.7","93","2022/10/21 16:14:00","0.0"
"22","1008.9","1011.1","2022/10/22 23:04:00","1006.8","2022/10/22 14:49:00","27.1","31.4","2022/10/22 12:14:00","24.5","2022/10/22 23:43:00","81","66","2022/10/22 12:15:00","3.1","82","10.4","67","2022/10/22 13:00:00","3.5"
"23","1011.7","1013.4","2022/10/23 22:12:00","1009.8","2022/10/23 02:31:00","23.2","25.2","2022/10/23 00:01:00","21.4","2022/10/23 10:11:00","79","65","2022/10/23 22:52:00","2.1","56","11.5","71","2022/10/23 01:25:00","6.0"
"24","1014.3","1015.9","2022/10/24 09:39:00","1011.8","2022/10/24 02:29:00","22.5","25.1","2022/10/24 10:22:00","21.4","2022/10/24 04:49:00","59","50","2022/10/24 08:37:00","3.5","63","11.4","73","2022/10/24 11:11:00","0.0"
"25","1015.2","1017.1","2022/10/25 08:30:00","1013.9","2022/10/25 14:56:00","22.0","25.1","2022/10/25 11:39:00","20.9","2022/10/25 04:51:00","58","46","2022/10/25 11:40:00","3.4","66","13.9","92","2022/10/25 11:11:00","0.0"
"26","1012.2","1014.5","2022/10/26 00:01:00","1010.2","2022/10/26 14:45:00","24.1","28.1","2022/10/26 10:54:00","21.1","2022/10/26 00:01:00","61","50","2022/10/26 10:56:00","3.3","87","11.8","91","2022/10/26 14:08:00","0.0"
"27","1010.7","1012.2","2022/10/27 21:34:00","1009.0","2022/10/27 13:37:00","25.1","29.1","2022/10/27 12:28:00","22.7","2022/10/27 21:45:00","67","54","2022/10/27 12:29:00","3.8","90","12.4","68","2022/10/27 12:58:00","0.0"
"28","1010.9","1012.4","2022/10/28 08:49:00","1009.1","2022/10/28 14:16:00","24.2","28.3","2022/10/28 12:06:00","22.3","2022/10/28 07:00:00","77","59","2022/10/28 12:00:00","3.7","75","13.2","75","2022/10/28 22:25:00","2.5"
"29","1010.4","1012.1","2022/10/29 09:40:00","1008.9","2022/10/29 15:00:00","23.9","25.6","2022/10/29 08:46:00","23.2","2022/10/29 22:58:00","73","64","2022/10/29 08:47:00","5.2","68","15.3","67","2022/10/29 14:43:00","0.0"
"30","1007.0","1009.1","2022/10/30 00:01:00","1005.4","2022/10/30 23:57:00","24.0","24.9","2022/10/30 10:03:00","23.2","2022/10/30 01:08:00","80","75","2022/10/30 10:04:00","5.3","69","16.0","82","2022/10/30 14:21:00","2.0"
"31","1006.3","1010.2","2022/10/31 22:18:00","1004.2","2022/10/31 13:55:00","23.1","24.7","2022/10/31 01:42:00","20.0","2022/10/31 23:52:00","81","75","2022/10/31 14:56:00","3.0","62","17.5","90","2022/10/31 07:49:00","23.0"