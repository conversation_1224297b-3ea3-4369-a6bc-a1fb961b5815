"""
智能補下載腳本
只針對有部分資料的測站進行補下載，跳過完全沒有資料的測站
"""

import os
from pathlib import Path
from datetime import datetime, timedelta
from scrape_codis_date import generate_potential_stations, download_codis, cleanup_chrome_processes
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading

def analyze_incomplete_stations():
    """
    分析不完整的測站，區分為：
    1. 有部分資料的測站（值得補下載）
    2. 完全沒有資料的測站（可能不存在）
    """
    print("正在分析不完整測站...")
    
    all_stations = generate_potential_stations(test_mode=False)
    base_dir = Path("data/raw/weather/all_stations")
    
    partial_data_stations = []  # 有部分資料的測站
    no_data_stations = []       # 完全沒有資料的測站
    
    for station_id in all_stations:
        station_dir = base_dir / station_id
        
        if not station_dir.exists():
            no_data_stations.append(station_id)
            continue
            
        csv_files = list(station_dir.glob("*.csv"))
        file_count = len(csv_files)
        
        if file_count == 0:
            no_data_stations.append(station_id)
        elif file_count < 36:  # 應該有36個檔案
            # 計算缺失的檔案
            expected_files = []
            start_dt = datetime(2022, 7, 1)
            end_dt = datetime(2025, 6, 1)
            
            current_dt = start_dt
            while current_dt <= end_dt:
                filename = f"{station_id}-{current_dt.year}-{current_dt.month:02d}.csv"
                expected_files.append(filename)
                
                next_month = (current_dt.month % 12) + 1
                next_year = current_dt.year + (current_dt.month // 12)
                current_dt = datetime(next_year, next_month, 1)
            
            actual_files = [f.name for f in csv_files]
            missing_files = [f for f in expected_files if f not in actual_files]
            
            partial_data_stations.append({
                "station_id": station_id,
                "actual": file_count,
                "missing": len(missing_files),
                "missing_files": missing_files
            })
    
    return partial_data_stations, no_data_stations

def smart_redownload_missing(max_workers=5):
    """
    智能補下載：只針對有部分資料的測站
    """
    print("=" * 60)
    print("智能補下載工具")
    print("=" * 60)
    
    # 清理殘留進程
    cleanup_chrome_processes()
    
    # 分析測站
    partial_stations, no_data_stations = analyze_incomplete_stations()
    
    print(f"\n分析結果：")
    print(f"有部分資料的測站: {len(partial_stations)} 個")
    print(f"完全沒有資料的測站: {len(no_data_stations)} 個")
    
    if not partial_stations:
        print("\n沒有需要補下載的測站！")
        return
    
    # 顯示有部分資料的測站
    print(f"\n有部分資料的測站（值得補下載）：")
    for station in partial_stations[:10]:
        print(f"  {station['station_id']}: {station['actual']}/36 檔案 (缺少 {station['missing']} 個)")
    
    if len(partial_stations) > 10:
        print(f"  ... 還有 {len(partial_stations) - 10} 個測站")
    
    # 顯示完全沒有資料的測站（可能不存在）
    if no_data_stations:
        print(f"\n完全沒有資料的測站（可能不存在，跳過）：")
        for station in no_data_stations[:10]:
            print(f"  {station}")
        if len(no_data_stations) > 10:
            print(f"  ... 還有 {len(no_data_stations) - 10} 個測站")
    
    # 詢問是否繼續
    choice = input(f"\n是否對 {len(partial_stations)} 個有部分資料的測站進行補下載？(y/N): ")
    if choice.lower() != 'y':
        print("已取消補下載")
        return
    
    print(f"\n開始使用 {max_workers} 個執行緒進行智能補下載...")
    
    # 並行補下載
    completed_count = 0
    total_success = 0
    total_failed = 0
    
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        # 提交補下載任務
        future_to_station = {
            executor.submit(download_missing_files_for_station, station_info): station_info["station_id"]
            for station_info in partial_stations
        }
        
        # 處理完成的任務
        for future in as_completed(future_to_station):
            station_id = future_to_station[future]
            completed_count += 1
            
            try:
                result = future.result()
                total_success += result.get("success_count", 0)
                total_failed += len(result.get("failed_months", []))
                
                progress = (completed_count / len(partial_stations)) * 100
                print(f"進度: {completed_count}/{len(partial_stations)} ({progress:.1f}%) - {station_id}")
                
            except Exception as e:
                print(f"測站 {station_id} 補下載時發生錯誤: {e}")
                total_failed += 1
    
    # 最終統計
    print(f"\n=== 智能補下載完成摘要 ===")
    print(f"處理測站數: {len(partial_stations)}")
    print(f"成功下載檔案數: {total_success}")
    print(f"失敗檔案數: {total_failed}")
    
    # 清理殘留進程
    cleanup_chrome_processes()

def download_missing_files_for_station(station_info, max_retries=2):
    """
    為單一測站下載缺失的檔案
    """
    station_id = station_info["station_id"]
    missing_files = station_info["missing_files"]
    
    if not missing_files:
        return {"station_id": station_id, "status": "no_missing_files"}
    
    print(f"正在補下載測站 {station_id} 的 {len(missing_files)} 個缺失檔案...")
    
    # 將缺失檔案按年月分組
    missing_months = []
    for filename in missing_files:
        # 從檔名解析年月: C0A980-2022-07.csv
        parts = filename.replace('.csv', '').split('-')
        if len(parts) >= 3:
            year, month = parts[-2], parts[-1]
            missing_months.append(f"{year}/{month}")
    
    success_count = 0
    failed_months = []
    
    for month in missing_months:
        for attempt in range(max_retries):
            try:
                print(f"  下載 {station_id} - {month} (嘗試 {attempt + 1}/{max_retries})")
                
                station_dir = Path("data/raw/weather/all_stations") / station_id
                download_codis(
                    stn_id=station_id,
                    start_date=month,
                    end_date=month,
                    download_dir=station_dir
                )
                
                # 檢查檔案是否成功下載
                expected_filename = f"{station_id}-{month.replace('/', '-')}.csv"
                if (station_dir / expected_filename).exists():
                    success_count += 1
                    print(f"  ✓ 成功下載 {month}")
                    break
                else:
                    print(f"  ✗ 下載失敗 {month} (檔案不存在)")
                    
            except Exception as e:
                print(f"  ✗ 下載失敗 {month}: {e}")
                if attempt == max_retries - 1:
                    failed_months.append(month)
    
    return {
        "station_id": station_id,
        "status": "completed",
        "success_count": success_count,
        "failed_months": failed_months
    }

if __name__ == "__main__":
    smart_redownload_missing(max_workers=5)
