﻿"觀測時間(day)","測站氣壓(hPa)","測站最高氣壓(hPa)","測站最高氣壓時間(LST)","測站最低氣壓(hPa)","測站最低氣壓時間(LST)","氣溫(℃)","最高氣溫(℃)","最高氣溫時間(LST)","最低氣溫(℃)","最低氣溫時間(LST)","相對溼度(%)","最小相對溼度(%)","最小相對溼度時間(LST)","風速(m/s)","風向(360degree)","最大瞬間風(m/s)","最大瞬間風風向(360degree)","最大瞬間風風速時間(LST)","降水量(mm)"
"ObsTime","StnPres","StnPresMax","StnPresMaxTime","StnPresMin","StnPresMinTime","Temperature","T Max","T Max Time","<PERSON> <PERSON>","T Min Time","RH","RHMin","RHMinTime","WS","WD","WSGust","WDGust","WGustTime","Precp"
"01","964.9","966.0","2023/09/01 09:43:00","963.4","2023/09/01 16:51:00","25.6","30.0","2023/09/01 13:12:00","23.5","2023/09/01 04:21:00","95","79","2023/09/01 13:12:00","2.7","51","9.9","48","2023/09/01 17:04:00","43.0"
"02","963.6","964.9","2023/09/02 09:12:00","962.5","2023/09/02 03:31:00","25.8","27.7","2023/09/02 12:51:00","24.4","2023/09/02 01:41:00","95","90","2023/09/02 12:51:00","4.0","42","14.3","93","2023/09/02 23:02:00","7.5"
"03","961.3","963.7","2023/09/03 00:01:00","959.2","2023/09/03 14:51:00","24.5","25.8","2023/09/03 09:31:00","22.5","2023/09/03 18:11:00","97","95","2023/09/03 11:01:00","5.3","53","15.4","84","2023/09/03 10:51:00","15.0"
"04","961.8","964.8","2023/09/04 22:52:00","959.3","2023/09/04 03:31:00","24.6","25.5","2023/09/04 11:51:00","24.0","2023/09/04 01:11:00","97","95","2023/09/04 23:41:00","3.2","54","13.2","58","2023/09/04 02:09:00","34.0"
"05","966.6","969.2","2023/09/05 20:51:00","964.1","2023/09/05 02:11:00","26.0","31.0","2023/09/05 11:41:00","23.9","2023/09/05 04:11:00","91","67","2023/09/05 11:41:00","1.1","60","6.6","87","2023/09/05 14:48:00","1.0"
"06","969.6","971.5","2023/09/06 22:11:00","967.6","2023/09/06 03:01:00","24.6","27.7","2023/09/06 08:35:00","23.0","2023/09/06 04:31:00","96","88","2023/09/06 09:03:00","1.2","53","7.1","53","2023/09/06 13:09:00","9.0"
"07","970.2","971.6","2023/09/07 09:56:00","968.7","2023/09/07 03:41:00","23.9","26.8","2023/09/07 11:22:00","21.9","2023/09/07 03:31:00","93","85","2023/09/07 11:22:00","1.1","46","6.6","46","2023/09/07 12:47:00","1.0"
"08","970.2","971.4","2023/09/08 09:16:00","969.2","2023/09/08 02:53:00","23.5","25.8","2023/09/08 14:12:00","21.1","2023/09/08 22:42:00","92","80","2023/09/08 13:44:00","1.0","56","6.0","48","2023/09/08 06:39:00","4.5"
"09","969.4","970.4","2023/09/09 09:42:00","968.4","2023/09/09 13:41:00","24.0","29.0","2023/09/09 11:31:00","20.9","2023/09/09 23:51:00","85","58","2023/09/09 10:54:00","1.1","342","6.0","51","2023/09/09 13:40:00","0.0"
"10","968.7","969.8","2023/09/10 20:41:00","967.3","2023/09/10 14:21:00","24.1","29.0","2023/09/10 12:42:00","20.4","2023/09/10 03:21:00","88","61","2023/09/10 10:21:00","1.1","30","6.0","39","2023/09/10 10:38:00","0.0"
"11","968.4","969.9","2023/09/11 21:51:00","967.3","2023/09/11 10:17:00","23.7","29.2","2023/09/11 13:48:00","--","--","83","55","2023/09/11 14:46:00","0.9","328","4.1","115","2023/09/11 13:35:00","0.0"
"12","968.5","969.7","2023/09/12 21:02:00","966.7","2023/09/12 15:11:00","24.2","29.7","2023/09/12 10:25:00","19.9","2023/09/12 02:31:00","85","67","2023/09/12 09:32:00","1.0","39","5.5","48","2023/09/12 10:44:00","0.0"
"13","968.3","969.3","2023/09/13 21:11:00","967.2","2023/09/13 16:11:00","24.4","30.2","2023/09/13 11:12:00","20.7","2023/09/13 01:31:00","86","69","2023/09/13 09:35:00","0.8","42","6.6","45","2023/09/13 12:59:00","0.0"
"14","969.3","971.3","2023/09/14 21:11:00","968.0","2023/09/14 03:51:00","25.5","30.3","2023/09/14 12:57:00","21.0","2023/09/14 03:21:00","83","63","2023/09/14 10:33:00","1.1","34","5.5","32","2023/09/14 12:05:00","0.5"
"15","970.5","972.2","2023/09/15 21:31:00","969.1","2023/09/15 04:55:00","25.4","30.3","2023/09/15 12:31:00","22.8","2023/09/15 05:17:00","89","72","2023/09/15 12:26:00","0.8","324","6.0","94","2023/09/15 13:52:00","0.0"
"16","972.0","973.6","2023/09/16 09:32:00","970.9","2023/09/16 16:32:00","25.1","30.1","2023/09/16 12:06:00","21.7","2023/09/16 05:01:00","85","66","2023/09/16 10:13:00","0.8","1","5.5","196","2023/09/16 10:15:00","0.0"
"17","971.5","972.7","2023/09/17 10:02:00","970.3","2023/09/17 15:51:00","25.6","30.8","2023/09/17 14:51:00","22.2","2023/09/17 22:37:00","86","66","2023/09/17 08:31:00","0.6","85","3.5","46","2023/09/17 09:03:00","0.0"
"18","972.2","973.8","2023/09/18 20:31:00","970.6","2023/09/18 03:41:00","25.3","30.1","2023/09/18 11:35:00","20.4","2023/09/18 04:57:00","86","63","2023/09/18 08:06:00","1.1","32","6.0","55","2023/09/18 10:51:00","0.0"
"19","972.0","--","--","--","--","26.4","--","--","--","--","79","--","--","1.0","55","--","--","--","0.0"
"20","970.9","972.5","2023/09/20 08:41:00","969.4","2023/09/20 16:41:00","26.5","30.2","2023/09/20 12:52:00","23.2","2023/09/20 04:42:00","79","66","2023/09/20 14:11:00","1.2","210","9.3","208","2023/09/20 00:52:00","0.0"
"21","971.3","972.8","2023/09/21 22:11:00","969.9","2023/09/21 01:37:00","25.9","30.1","2023/09/21 12:41:00","23.3","2023/09/21 00:13:00","86","71","2023/09/21 12:31:00","1.3","33","6.0","208","2023/09/21 04:16:00","0.0"
"22","971.4","973.2","2023/09/22 21:22:00","969.9","2023/09/22 03:31:00","25.9","30.1","2023/09/22 12:17:00","22.9","2023/09/22 06:03:00","89","72","2023/09/22 12:23:00","2.3","49","8.2","45","2023/09/22 12:17:00","4.5"
"23","971.3","972.6","2023/09/23 00:01:00","969.7","2023/09/23 15:05:00","26.0","30.1","2023/09/23 12:02:00","23.9","2023/09/23 04:51:00","89","73","2023/09/23 15:04:00","1.7","33","9.3","41","2023/09/23 04:44:00","16.0"
"24","971.4","972.8","2023/09/24 08:24:00","969.8","2023/09/24 14:52:00","25.8","30.4","2023/09/24 12:35:00","22.6","2023/09/24 05:22:00","88","67","2023/09/24 11:11:00","1.5","52","8.2","53","2023/09/24 14:41:00","0.0"
"25","971.5","972.7","2023/09/25 09:18:00","970.3","2023/09/25 03:34:00","25.4","29.2","2023/09/25 09:05:00","22.0","2023/09/25 03:21:00","90","74","2023/09/25 09:05:00","1.8","44","6.6","43","2023/09/25 10:08:00","0.0"
"26","971.8","973.2","2023/09/26 08:52:00","970.6","2023/09/26 15:07:00","25.8","29.7","2023/09/26 12:21:00","24.1","2023/09/26 03:51:00","91","72","2023/09/26 13:51:00","2.3","38","7.7","35","2023/09/26 19:33:00","2.5"
"27","971.5","972.7","2023/09/27 22:41:00","970.0","2023/09/27 14:21:00","25.7","29.7","2023/09/27 11:22:00","23.6","2023/09/27 07:06:00","91","76","2023/09/27 11:22:00","2.9","42","9.3","38","2023/09/27 06:45:00","0.0"
"28","972.0","973.6","2023/09/28 22:23:00","970.9","2023/09/28 14:01:00","25.6","29.0","2023/09/28 13:23:00","23.2","2023/09/28 07:16:00","92","77","2023/09/28 15:11:00","2.3","61","8.2","37","2023/09/28 12:00:00","3.5"
"29","972.5","974.1","2023/09/29 10:22:00","970.8","2023/09/29 17:02:00","25.3","29.4","2023/09/29 11:33:00","23.9","2023/09/29 23:25:00","94","81","2023/09/29 11:12:00","2.4","45","8.8","95","2023/09/29 10:58:00","20.0"
"30","970.7","972.1","2023/09/30 00:01:00","968.9","2023/09/30 15:17:00","24.8","29.3","2023/09/30 13:02:00","22.8","2023/09/30 03:01:00","94","82","2023/09/30 13:02:00","1.9","36","6.6","52","2023/09/30 09:37:00","0.5"