# 農業氣象站資料下載工具使用說明

## 📋 功能概述

這個工具專門用於下載台灣農業氣象站的月報表資料，時間範圍為 2023年7月 到 2025年6月。

## ✨ 主要特點

- **完整農業站清單**：包含127個農業氣象站代號
- **自動選項設定**：自動取消"署屬有人站"，選擇"農業站"
- **並行下載**：支援1-12個視窗同時下載
- **強制超時控制**：避免程式無限運行
- **測試模式**：可先測試少數農業站驗證功能
- **自動檔案整理**：下載檔案自動分站點存放

## 🚀 使用方法

### 1. 運行程式
```bash
python download_agricultural_stations.py
```

### 2. 選擇模式
- **測試模式**：下載前5個農業站的2個月資料（推薦首次使用）
- **完整模式**：下載所有127個農業站的完整資料

### 3. 設定並行視窗數
- 測試模式：1-3個視窗
- 完整模式：1-12個視窗
- 建議根據電腦效能選擇適當數量

## 📁 檔案結構

下載的檔案會自動整理到以下目錄結構：

```
data/raw/weather/agricultural_weather_station/
├── V2K620/          # 麥寮合作社
│   ├── V2K620-2023-07.csv
│   ├── V2K620-2023-08.csv
│   └── ...
├── V2K610/          # 大庄合作社
│   ├── V2K610-2023-07.csv
│   └── ...
└── ...
```

## 🏢 包含的農業站

### 主要地區分布：
- **雲林地區**：麥寮合作社、大庄合作社等
- **桃園地區**：八德蔬果、八德合作社等
- **南投地區**：臺大竹山、溪頭等
- **苗栗地區**：種苗改良場、苗栗農改場等
- **各農改場**：台中、桃園、高雄、花蓮等農改場
- **茶改場**：各分場
- **畜試所**：各區分所
- **其他**：林試所、水試所等研究單位

總計：**127個農業氣象站**

## ⚠️ 注意事項

1. **網路連線**：確保網路連線穩定
2. **瀏覽器**：需要安裝Chrome瀏覽器
3. **執行時間**：完整模式可能需要數小時
4. **磁碟空間**：確保有足夠空間存放檔案
5. **超時控制**：程式有自動超時機制，避免卡住

## 🔧 故障排除

### 如果程式卡住：
1. 按 Ctrl+C 中斷程式
2. 運行 `python emergency_stop.py` 清理進程
3. 重新啟動程式

### 如果下載失敗：
1. 檢查網路連線
2. 確認農業站代號是否正確
3. 先使用測試模式驗證

### 如果檔案不完整：
1. 檢查下載目錄
2. 查看錯誤訊息
3. 重新運行程式（會自動跳過已下載的檔案）

## 📊 預期結果

- **測試模式**：5個農業站 × 2個月 = 10個CSV檔案
- **完整模式**：127個農業站 × 23個月 = 約2,921個CSV檔案

每個CSV檔案包含該農業站該月份的詳細氣象觀測資料。

## 🎯 建議使用流程

1. **首次使用**：選擇測試模式，使用2-3個視窗
2. **驗證結果**：檢查下載的檔案是否正確
3. **完整下載**：確認無誤後使用完整模式
4. **監控進度**：觀察下載進度和錯誤訊息
5. **檢查結果**：下載完成後檢查檔案完整性

這個工具將為您的蔬菜價格預測項目提供完整的農業氣象資料基礎！
