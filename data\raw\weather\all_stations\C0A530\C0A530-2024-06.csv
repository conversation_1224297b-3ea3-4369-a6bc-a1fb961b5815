﻿"觀測時間(day)","測站氣壓(hPa)","測站最高氣壓(hPa)","測站最高氣壓時間(LST)","測站最低氣壓(hPa)","測站最低氣壓時間(LST)","氣溫(℃)","最高氣溫(℃)","最高氣溫時間(LST)","最低氣溫(℃)","最低氣溫時間(LST)","相對溼度(%)","最小相對溼度(%)","最小相對溼度時間(LST)","風速(m/s)","風向(360degree)","最大瞬間風(m/s)","最大瞬間風風向(360degree)","最大瞬間風風速時間(LST)","降水量(mm)"
"ObsTime","StnPres","StnPresMax","StnPresMaxTime","StnPresMin","StnPresMinTime","Temperature","T Max","T Max Time","<PERSON> <PERSON>","T Min Time","RH","RHMin","RHMinTime","WS","WD","WSGust","WDGust","WGustTime","Precp"
"01","/","--","--","--","--","/","--","--","--","--","/","--","--","/","/","--","--","--","24.0"
"02","/","--","--","--","--","/","--","--","--","--","/","--","--","/","/","--","--","--","20.5"
"03","/","--","--","--","--","/","--","--","--","--","/","--","--","/","/","--","--","--","2.5"
"04","/","--","--","--","--","/","--","--","--","--","/","--","--","/","/","--","--","--","0.0"
"05","/","--","--","--","--","/","--","--","--","--","/","--","--","/","/","--","--","--","4.0"
"06","/","--","--","--","--","/","--","--","--","--","/","--","--","/","/","--","--","--","8.0"
"07","/","--","--","--","--","/","--","--","--","--","/","--","--","/","/","--","--","--","0.5"
"08","/","--","--","--","--","/","--","--","--","--","/","--","--","/","/","--","--","--","0.0"
"09","/","--","--","--","--","/","--","--","--","--","/","--","--","/","/","--","--","--","18.0"
"10","/","--","--","--","--","/","--","--","--","--","/","--","--","/","/","--","--","--","0.0"
"11","/","--","--","--","--","/","--","--","--","--","/","--","--","/","/","--","--","--","0.0"
"12","/","--","--","--","--","/","--","--","--","--","/","--","--","/","/","--","--","--","0.0"
"13","/","--","--","--","--","/","--","--","--","--","/","--","--","/","/","--","--","--","0.0"
"14","971.4","973.2","2024/06/14 22:53:00","969.5","2024/06/14 15:23:00","26.3","29.9","2024/06/14 16:53:00","23.8","2024/06/14 23:51:00","87","67","2024/06/14 18:03:00","0.2","0","3.5","144","2024/06/14 18:02:00","6.0"
"15","972.8","976.0","2024/06/15 23:13:00","970.3","2024/06/15 04:41:00","26.5","34.9","2024/06/15 10:51:00","23.0","2024/06/15 05:01:00","87","48","2024/06/15 10:43:00","0.2","0","4.9","56","2024/06/15 16:59:00","0.0"
"16","975.2","976.5","2024/06/16 23:01:00","973.8","2024/06/16 15:12:00","26.2","32.5","2024/06/16 09:38:00","23.4","2024/06/16 04:31:00","90","59","2024/06/16 09:41:00","0.1","0","7.7","91","2024/06/16 15:18:00","15.5"
"17","974.6","976.3","2024/06/17 00:01:00","972.4","2024/06/17 16:13:00","26.3","32.7","2024/06/17 13:33:00","23.5","2024/06/17 02:51:00","91","54","2024/06/17 13:37:00","0.1","0","4.9","206","2024/06/17 17:00:00","11.0"
"18","975.1","976.6","2024/06/18 22:01:00","973.6","2024/06/18 03:31:00","25.4","30.2","2024/06/18 11:38:00","23.5","2024/06/18 23:31:00","96","68","2024/06/18 11:23:00","0.0","0","3.0","229","2024/06/18 13:17:00","4.5"
"19","974.7","976.0","2024/06/19 08:31:00","973.2","2024/06/19 13:52:00","26.2","33.4","2024/06/19 11:03:00","23.4","2024/06/19 04:01:00","91","59","2024/06/19 13:43:00","0.1","0","3.0","20","2024/06/19 10:40:00","3.5"
"20","973.8","975.2","2024/06/20 08:27:00","972.1","2024/06/20 15:41:00","27.2","34.6","2024/06/20 12:43:00","23.7","2024/06/20 05:21:00","85","54","2024/06/20 12:02:00","0.1","0","4.1","20","2024/06/20 14:41:00","0.0"
"21","973.8","975.3","2024/06/21 08:31:00","972.1","2024/06/21 14:36:00","27.3","34.9","2024/06/21 11:53:00","24.3","2024/06/21 05:21:00","89","51","2024/06/21 11:43:00","0.2","0","6.0","95","2024/06/21 13:28:00","1.0"
"22","974.2","976.3","2024/06/22 22:03:00","972.5","2024/06/22 15:53:00","28.4","35.3","2024/06/22 10:53:00","24.1","2024/06/22 05:21:00","85","53","2024/06/22 10:53:00","0.3","27","6.0","90","2024/06/22 14:25:00","0.0"
"23","975.3","977.4","2024/06/23 22:03:00","973.3","2024/06/23 14:41:00","26.8","34.1","2024/06/23 11:23:00","23.0","2024/06/23 23:51:00","88","57","2024/06/23 11:43:00","0.3","32","7.1","73","2024/06/23 13:45:00","0.0"
"24","976.8","979.1","2024/06/24 21:39:00","974.6","2024/06/24 14:21:00","26.0","35.1","2024/06/24 10:37:00","22.6","2024/06/24 03:51:00","88","50","2024/06/24 10:41:00","0.3","9","9.9","340","2024/06/24 15:34:00","13.0"
"25","978.4","981.3","2024/06/25 21:23:00","976.3","2024/06/25 14:22:00","26.6","35.0","2024/06/25 13:43:00","22.5","2024/06/25 03:01:00","84","43","2024/06/25 13:43:00","0.3","26","4.9","84","2024/06/25 12:14:00","6.0"
"26","980.2","981.3","2024/06/26 22:03:00","978.8","2024/06/26 15:42:00","26.8","34.1","2024/06/26 11:04:00","23.2","2024/06/26 04:41:00","85","49","2024/06/26 11:10:00","0.2","0","7.7","151","2024/06/26 15:02:00","0.0"
"27","979.5","--","--","--","--","27.7","--","--","--","--","79","--","--","0.5","44","--","--","--","0.0"
"28","977.8","979.2","2024/06/28 06:23:00","976.0","2024/06/28 13:11:00","25.7","35.2","2024/06/28 11:17:00","22.9","2024/06/28 23:31:00","90","49","2024/06/28 11:14:00","0.2","0","6.6","47","2024/06/28 12:53:00","29.0"
"29","976.1","977.5","2024/06/29 00:01:00","973.9","2024/06/29 15:11:00","26.7","34.5","2024/06/29 12:03:00","22.2","2024/06/29 04:31:00","86","53","2024/06/29 11:15:00","0.3","15","6.6","64","2024/06/29 13:17:00","0.0"
"30","974.9","976.4","2024/06/30 08:11:00","973.3","2024/06/30 16:11:00","27.1","34.7","2024/06/30 10:55:00","24.5","2024/06/30 04:01:00","90","51","2024/06/30 10:55:00","0.2","0","5.5","354","2024/06/30 09:38:00","5.0"