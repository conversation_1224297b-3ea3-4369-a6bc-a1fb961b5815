"""
創建包含已知存在的自動氣象站清單
只使用確認存在的測站代號
"""

import pandas as pd
import os
from pathlib import Path

def create_known_station_list():
    """
    創建包含已知存在的自動氣象站清單
    只使用確認可以正常運作的測站代號
    """
    
    # 已知存在且可正常運作的自動氣象站清單
    # 這些測站代號已經在原始程式中驗證過
    stations_data = {
        "站號": [
            "C0A980",  # 桃山
            "C0B100",  # 台北  
            "C0C480"   # 台中
        ],
        
        "站名": [
            "桃山",
            "台北", 
            "台中"
        ],
        
        "緯度": [
            24.4327,  # 桃山
            25.0330,  # 台北
            24.1477   # 台中
        ],
        
        "經度": [
            121.3038,  # 桃山
            121.5654,  # 台北
            120.6736   # 台中
        ]
    }
    
    # 創建 DataFrame
    stations_df = pd.DataFrame(stations_data)
    
    # 確保目錄存在
    stations_dir = Path("data/interim/stations")
    stations_dir.mkdir(parents=True, exist_ok=True)
    
    # 儲存到檔案
    stations_file = stations_dir / "stations.csv"
    stations_df.to_csv(stations_file, index=False, encoding="utf-8-sig")
    
    print(f"已創建包含 {len(stations_df)} 個已知測站的清單")
    print(f"檔案儲存位置: {stations_file}")
    
    # 顯示測站資訊
    print("\n測站清單:")
    print(stations_df.to_string(index=False))
    
    return stations_df

if __name__ == "__main__":
    create_known_station_list()
