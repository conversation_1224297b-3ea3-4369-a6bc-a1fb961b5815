"""
測試已知測站的批量下載功能
使用確認存在的3個測站進行測試
"""

import pandas as pd
import os
from pathlib import Path
from scrape_codis_date import download_codis

def test_known_stations_download():
    """
    測試已知測站的批量下載功能
    """
    print("開始測試已知測站的批量下載功能...")
    
    # 讀取測站清單
    stations_file = Path("data/interim/stations/stations.csv")
    if not stations_file.exists():
        print("測站清單檔案不存在，請先執行 create_known_stations.py")
        return
    
    stations_df = pd.read_csv(stations_file)
    print(f"測試下載 {len(stations_df)} 個已知測站的資料")
    
    # 設定下載參數 - 先測試一個月的資料
    start_date = "2024/01"  
    end_date = "2024/01"
    base_download_dir = Path("data/raw/weather/known_stations_test")
    
    # 創建基礎下載目錄
    os.makedirs(base_download_dir, exist_ok=True)
    
    # 記錄結果
    success_count = 0
    failed_stations = []
    
    # 逐個測站下載
    for index, row in stations_df.iterrows():
        station_id = row["站號"]
        station_name = row["站名"]
        
        print(f"\n進度: {index + 1}/{len(stations_df)}")
        print(f"正在處理測站: {station_id} ({station_name})")
        
        try:
            # 為每個測站創建子目錄
            station_dir = base_download_dir / station_id
            os.makedirs(station_dir, exist_ok=True)
            
            # 下載該測站的資料
            download_codis(
                stn_id=station_id,
                start_date=start_date,
                end_date=end_date,
                download_dir=station_dir
            )
            
            # 檢查是否有檔案被下載
            csv_files = list(station_dir.glob("*.csv"))
            if csv_files:
                success_count += 1
                print(f"測站 {station_id} ({station_name}) 下載完成，共 {len(csv_files)} 個檔案")
                for file in csv_files:
                    size_kb = file.stat().st_size / 1024
                    print(f"  - {file.name} ({size_kb:.1f} KB)")
            else:
                print(f"測站 {station_id} ({station_name}) 下載完成但未發現檔案")
                failed_stations.append(f"{station_id} ({station_name}) - 無檔案")
            
        except Exception as e:
            print(f"測站 {station_id} ({station_name}) 下載失敗: {e}")
            failed_stations.append(f"{station_id} ({station_name}) - 錯誤: {e}")
            continue
    
    # 輸出測試結果
    print(f"\n=== 測試結果摘要 ===")
    print(f"成功下載: {success_count}/{len(stations_df)} 個測站")
    
    if failed_stations:
        print(f"失敗測站數量: {len(failed_stations)}")
        print("失敗測站清單:")
        for station in failed_stations:
            print(f"  - {station}")
    
    print(f"測試檔案已下載到: {base_download_dir}")
    
    # 檢查總下載檔案數
    total_files = 0
    total_size = 0
    for station_dir in base_download_dir.iterdir():
        if station_dir.is_dir():
            files = list(station_dir.glob("*.csv"))
            total_files += len(files)
            for file in files:
                total_size += file.stat().st_size
    
    print(f"\n總計下載: {total_files} 個檔案，總大小: {total_size/1024:.1f} KB")

if __name__ == "__main__":
    test_known_stations_download()
