"""
測試單一測站下載功能
使用已知存在的測站 C0A980 (桃山)
"""

from scrape_codis_date import download_codis
from pathlib import Path
import os

def test_single_station():
    """
    測試單一測站下載功能
    """
    print("開始測試單一測站下載功能...")
    
    # 使用已知存在的測站
    station_id = "C0A980"  # 桃山測站
    start_date = "2024/01"
    end_date = "2024/01"
    
    # 設定下載目錄
    download_dir = Path("data/raw/weather/single_test").resolve()
    
    print(f"測站: {station_id}")
    print(f"時間範圍: {start_date} 到 {end_date}")
    print(f"下載目錄: {download_dir}")
    
    try:
        # 清理舊的測試檔案
        if download_dir.exists():
            for file in download_dir.glob("*.csv"):
                file.unlink()
                print(f"已刪除舊檔案: {file.name}")
        
        # 下載資料
        download_codis(
            stn_id=station_id,
            start_date=start_date,
            end_date=end_date,
            download_dir=download_dir
        )
        
        # 檢查下載結果
        print(f"\n=== 下載結果檢查 ===")
        csv_files = list(download_dir.glob("*.csv"))
        
        if csv_files:
            print(f"成功下載 {len(csv_files)} 個檔案:")
            for file in csv_files:
                size_kb = file.stat().st_size / 1024
                print(f"  - {file.name} ({size_kb:.1f} KB)")
                
                # 檢查檔案內容
                try:
                    with open(file, 'r', encoding='utf-8') as f:
                        first_line = f.readline().strip()
                        print(f"    檔案開頭: {first_line[:100]}...")
                except Exception as e:
                    print(f"    讀取檔案時發生錯誤: {e}")
        else:
            print("未發現任何 CSV 檔案")
            
            # 檢查目錄中的所有檔案
            all_files = list(download_dir.glob("*"))
            if all_files:
                print("目錄中的其他檔案:")
                for file in all_files:
                    print(f"  - {file.name}")
            else:
                print("下載目錄為空")
        
        print(f"\n測試完成")
        
    except Exception as e:
        print(f"測試失敗: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_single_station()
