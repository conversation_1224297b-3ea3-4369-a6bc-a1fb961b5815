"""
自動化測試農業站下載功能
"""

from download_agricultural_stations import test_agricultural_stations, cleanup_chrome_processes
import sys

def run_automated_test():
    """
    自動化運行農業站下載測試
    """
    print("=" * 60)
    print("自動化農業站下載測試")
    print("=" * 60)
    
    # 清理殘留進程
    print("清理殘留的Chrome進程...")
    cleanup_chrome_processes()
    
    # 設定測試參數
    test_stations = ["V2K620", "V2K610", "V2C260"]  # 測試前3個農業站
    max_workers = 2  # 使用2個並行視窗
    
    print(f"測試農業站: {test_stations}")
    print(f"並行視窗數: {max_workers}")
    print(f"下載時間範圍: 2023/07 到 2023/08 (2個月)")
    print()
    
    try:
        print("開始自動化測試...")
        results = test_agricultural_stations(
            test_stations=test_stations,
            max_workers=max_workers
        )
        
        print("\n=== 測試完成 ===")
        if results:
            success_count = sum(1 for r in results if r.get("status") == "success")
            total_files = sum(r.get("files", 0) for r in results if r.get("status") == "success")
            
            print(f"成功農業站數: {success_count}/{len(test_stations)}")
            print(f"總下載檔案數: {total_files}")
            
            # 顯示詳細結果
            for result in results:
                station_id = result.get("station_id", "未知")
                status = result.get("status", "未知")
                files = result.get("files", 0)
                
                if status == "success":
                    print(f"✓ {station_id}: 成功下載 {files} 個檔案")
                elif status == "no_data":
                    print(f"⚠ {station_id}: 無資料")
                elif status == "error":
                    error = result.get("error", "未知錯誤")
                    print(f"✗ {station_id}: 錯誤 - {error}")
                else:
                    print(f"? {station_id}: {status}")
        
        print("\n測試完成！")
        return True
        
    except KeyboardInterrupt:
        print("\n測試被用戶中斷")
        return False
    except Exception as e:
        print(f"\n測試過程中發生錯誤: {e}")
        return False
    finally:
        print("清理殘留進程...")
        cleanup_chrome_processes()

if __name__ == "__main__":
    success = run_automated_test()
    sys.exit(0 if success else 1)
