import requests
from pprint import pprint

url = "https://codis.cwa.gov.tw/api/station?"
headers = {
    "accept": "application/json, text/javascript, */*; q=0.01",
    "content-type": "application/x-www-form-urlencoded; charset=UTF-8",
    "x-requested-with": "XMLHttpRequest",
}
data = {
    "date": "2025-07-10T00:00:00.000+08:00",
    "type": "report_date",
    "stn_ID": "C0F9N0",
    "stn_type": "auto_C0",
    "more": "",
    "start": "2025-07-10T00:00:00",
    "end": "2025-07-13T23:59:59",
    "item": "",
}

response = requests.post(url, headers=headers, data=data, verify=False)

pprint(response.json())
