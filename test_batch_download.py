"""
測試批量下載功能
先測試少數幾個測站，確認功能正常後再進行全部下載
"""

import pandas as pd
import os
from pathlib import Path
from scrape_codis_date import download_codis

def test_batch_download():
    """
    測試批量下載功能，只下載前3個測站的資料
    """
    print("開始測試批量下載功能...")
    
    # 讀取測站清單
    stations_file = Path("data/interim/stations/stations.csv")
    if not stations_file.exists():
        print("測站清單檔案不存在，請先執行 create_station_list.py")
        return
    
    stations_df = pd.read_csv(stations_file)
    
    # 只測試前3個測站
    test_stations = stations_df.head(3)
    print(f"測試下載 {len(test_stations)} 個測站的資料")
    
    # 設定下載參數
    start_date = "2024/01"  # 只下載一個月的資料進行測試
    end_date = "2024/01"
    base_download_dir = Path("data/raw/weather/test_batch")
    
    # 創建基礎下載目錄
    os.makedirs(base_download_dir, exist_ok=True)
    
    # 記錄結果
    success_count = 0
    failed_stations = []
    
    # 逐個測站下載
    for index, row in test_stations.iterrows():
        station_id = row["站號"]
        station_name = row["站名"]
        
        print(f"\n進度: {index + 1}/{len(test_stations)}")
        print(f"正在處理測站: {station_id} ({station_name})")
        
        try:
            # 為每個測站創建子目錄
            station_dir = base_download_dir / station_id
            os.makedirs(station_dir, exist_ok=True)
            
            # 下載該測站的資料
            download_codis(
                stn_id=station_id,
                start_date=start_date,
                end_date=end_date,
                download_dir=station_dir
            )
            
            success_count += 1
            print(f"測站 {station_id} ({station_name}) 下載完成")
            
        except Exception as e:
            print(f"測站 {station_id} ({station_name}) 下載失敗: {e}")
            failed_stations.append(f"{station_id} ({station_name})")
            continue
    
    # 輸出測試結果
    print(f"\n=== 測試結果摘要 ===")
    print(f"成功下載: {success_count}/{len(test_stations)} 個測站")
    
    if failed_stations:
        print(f"失敗測站數量: {len(failed_stations)}")
        print("失敗測站清單:")
        for station in failed_stations:
            print(f"  - {station}")
    
    print(f"測試檔案已下載到: {base_download_dir}")
    
    # 檢查下載的檔案
    print(f"\n=== 下載檔案檢查 ===")
    for station_dir in base_download_dir.iterdir():
        if station_dir.is_dir():
            files = list(station_dir.glob("*.csv"))
            print(f"{station_dir.name}: {len(files)} 個檔案")
            for file in files:
                print(f"  - {file.name} ({file.stat().st_size} bytes)")

if __name__ == "__main__":
    test_batch_download()
