"""
調試 CODiS 網站，檢查網站結構
"""

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
import time

def debug_codis_website():
    """
    調試 CODiS 網站，檢查當前的網站結構
    """
    print("正在調試 CODiS 網站...")
    
    # 設定 Chrome 瀏覽器選項
    chrome_options = webdriver.ChromeOptions()
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--disable-gpu")
    chrome_options.add_argument("--disable-logging")
    chrome_options.add_argument("--disable-extensions")
    # 暫時不使用無頭模式，以便觀察網站
    # chrome_options.add_argument("--headless=new")
    
    service = Service(ChromeDriverManager().install())
    driver = webdriver.Chrome(service=service, options=chrome_options)
    wait = WebDriverWait(driver, 30)
    
    try:
        # 開啟 CODiS 網站
        print("正在開啟 CODiS 網站...")
        driver.get("https://codis.cwa.gov.tw/StationData")
        time.sleep(5)
        
        # 檢查頁面標題
        print(f"頁面標題: {driver.title}")
        
        # 檢查頁面是否載入完成
        print("檢查頁面載入狀態...")
        try:
            # 等待頁面載入完成
            wait.until(lambda driver: driver.execute_script("return document.readyState") == "complete")
            print("頁面載入完成")
        except Exception as e:
            print(f"頁面載入檢查失敗: {e}")
        
        # 檢查是否有自動氣象站選項
        print("檢查自動氣象站選項...")
        try:
            # 嘗試不同的選擇器
            selectors = [
                "//div[@class='form-check' and .//input[@value='auto_C0']]",
                "//input[@value='auto_C0']",
                "//label[contains(text(), '自動氣象站')]",
                "//div[contains(@class, 'form-check')]",
                "//input[contains(@value, 'auto')]",
                "//input[contains(@value, 'C0')]"
            ]
            
            for i, selector in enumerate(selectors):
                try:
                    elements = driver.find_elements(By.XPATH, selector)
                    print(f"選擇器 {i+1}: {selector}")
                    print(f"  找到 {len(elements)} 個元素")
                    if elements:
                        for j, element in enumerate(elements[:3]):  # 只顯示前3個
                            try:
                                print(f"  元素 {j+1}: {element.tag_name}, text='{element.text}', value='{element.get_attribute('value')}'")
                            except:
                                print(f"  元素 {j+1}: 無法獲取詳細資訊")
                except Exception as e:
                    print(f"選擇器 {i+1} 失敗: {e}")
        
        except Exception as e:
            print(f"檢查自動氣象站選項失敗: {e}")
        
        # 檢查頁面源碼中是否包含相關關鍵字
        print("檢查頁面源碼...")
        page_source = driver.page_source
        keywords = ["auto_C0", "自動氣象站", "C0A980", "form-check", "StationData"]
        for keyword in keywords:
            if keyword in page_source:
                print(f"  找到關鍵字: {keyword}")
            else:
                print(f"  未找到關鍵字: {keyword}")
        
        # 暫停讓用戶觀察
        print("\n程式將暫停30秒，請觀察瀏覽器視窗...")
        time.sleep(30)
        
    except Exception as e:
        print(f"調試過程中發生錯誤: {e}")
        import traceback
        traceback.print_exc()
        
    finally:
        driver.quit()

if __name__ == "__main__":
    debug_codis_website()
