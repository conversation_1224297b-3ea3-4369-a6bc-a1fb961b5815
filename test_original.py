"""
測試原始程式是否能正常運行
"""

from scrape_codis_date import get_stn_id, download_codis

def test_original_function():
    """
    測試原始程式的功能
    """
    print("測試原始程式功能...")
    
    try:
        # 測試根據經緯度找測站
        print("測試根據經緯度找測站...")
        stn_id = get_stn_id(121.3038, 24.4327)  # 桃山地區
        print(f"找到最近的測站: {stn_id}")
        
        if stn_id:
            # 測試下載單個測站的一個月資料
            print(f"\n測試下載測站 {stn_id} 的 2024/01 資料...")
            download_codis(
                stn_id=stn_id,
                start_date="2024/01",
                end_date="2024/01",
                download_dir="data/raw/weather/original_test"
            )
            print("原始程式測試完成")
        else:
            print("無法找到測站")
            
    except Exception as e:
        print(f"原始程式測試失敗: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_original_function()
