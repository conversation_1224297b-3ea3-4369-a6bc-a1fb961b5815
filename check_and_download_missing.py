"""
檢測缺失的農業站檔案並針對性下載
只下載缺失的月份，不重複下載已存在的檔案
"""

import os
from pathlib import Path
from datetime import datetime, timedelta
from dateutil.relativedelta import relativedelta
from download_agricultural_stations import (
    AGRICULTURAL_STATIONS, 
    download_single_station_month,
    cleanup_chrome_processes
)
from concurrent.futures import ThreadPoolExecutor, as_completed
import time

def generate_expected_months():
    """
    生成預期的月份清單：2023年7月到2025年6月（24個月）
    """
    start_date = datetime(2023, 7, 1)
    months = []
    
    for i in range(24):  # 24個月
        current_date = start_date + relativedelta(months=i)
        month_str = current_date.strftime("%Y-%m")
        months.append(month_str)
    
    return months

def check_missing_files():
    """
    檢查每個農業站缺失的檔案
    返回：{station_id: [missing_months]}
    """
    expected_months = generate_expected_months()
    base_dir = Path("data/raw/weather/agricultural_weather_station")
    missing_files = {}
    
    print("檢查缺失檔案...")
    print(f"預期月份數：{len(expected_months)}")
    print(f"月份範圍：{expected_months[0]} 到 {expected_months[-1]}")
    print()
    
    total_expected = 0
    total_existing = 0
    
    for station_id in AGRICULTURAL_STATIONS:
        station_dir = base_dir / station_id
        missing_months = []
        
        for month in expected_months:
            expected_file = station_dir / f"{station_id}-{month}.csv"
            if not expected_file.exists():
                missing_months.append(month)
        
        if missing_months:
            missing_files[station_id] = missing_months
        
        existing_count = len(expected_months) - len(missing_months)
        total_expected += len(expected_months)
        total_existing += existing_count
        
        print(f"{station_id}: {existing_count}/{len(expected_months)} 檔案存在，缺失 {len(missing_months)} 個")
    
    print()
    print(f"總計：{total_existing}/{total_expected} 檔案存在")
    print(f"缺失檔案總數：{total_expected - total_existing}")
    print(f"需要下載的農業站數：{len(missing_files)}")
    
    return missing_files

def download_missing_station_files(station_id, missing_months, thread_id):
    """
    下載單一農業站的缺失檔案
    """
    print(f"[執行緒 {thread_id}] 開始處理 {station_id}，需下載 {len(missing_months)} 個月份")
    
    success_count = 0
    error_count = 0
    
    for month in missing_months:
        try:
            year, month_num = month.split('-')
            month_str = f"{year}/{month_num}"
            
            print(f"[執行緒 {thread_id}] {station_id} 下載 {month_str}")
            
            result = download_single_station_month(
                station_id=station_id,
                year_month=month_str,
                thread_id=thread_id,
                timeout_seconds=180  # 增加到3分鐘
            )
            
            if result.get("status") == "success":
                success_count += 1
                print(f"[執行緒 {thread_id}] ✓ {station_id} {month_str} 下載成功")
            else:
                error_count += 1
                error_msg = result.get("error", "未知錯誤")
                print(f"[執行緒 {thread_id}] ✗ {station_id} {month_str} 下載失敗: {error_msg}")
                
        except Exception as e:
            error_count += 1
            print(f"[執行緒 {thread_id}] ✗ {station_id} {month} 發生異常: {e}")
    
    print(f"[執行緒 {thread_id}] {station_id} 完成：成功 {success_count}，失敗 {error_count}")
    
    return {
        "station_id": station_id,
        "success_count": success_count,
        "error_count": error_count,
        "total_attempted": len(missing_months)
    }

def parallel_download_missing_files(missing_files, max_workers=8):
    """
    並行下載缺失檔案
    """
    if not missing_files:
        print("沒有缺失檔案需要下載！")
        return
    
    print(f"開始並行下載缺失檔案，使用 {max_workers} 個執行緒")
    print("=" * 60)
    
    start_time = time.time()
    results = []
    
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        # 提交所有下載任務
        future_to_station = {}
        thread_id = 1
        
        for station_id, missing_months in missing_files.items():
            future = executor.submit(
                download_missing_station_files,
                station_id,
                missing_months,
                thread_id
            )
            future_to_station[future] = station_id
            thread_id += 1
        
        # 收集結果
        for future in as_completed(future_to_station):
            station_id = future_to_station[future]
            try:
                result = future.result()
                results.append(result)
            except Exception as e:
                print(f"農業站 {station_id} 處理失敗: {e}")
                results.append({
                    "station_id": station_id,
                    "success_count": 0,
                    "error_count": 0,
                    "total_attempted": 0,
                    "error": str(e)
                })
    
    # 統計結果
    end_time = time.time()
    total_time = end_time - start_time
    
    total_success = sum(r.get("success_count", 0) for r in results)
    total_errors = sum(r.get("error_count", 0) for r in results)
    total_attempted = sum(r.get("total_attempted", 0) for r in results)
    
    print("=" * 60)
    print("下載完成統計")
    print("=" * 60)
    print(f"總執行時間: {total_time:.1f} 秒")
    print(f"處理農業站數: {len(results)}")
    print(f"嘗試下載檔案數: {total_attempted}")
    print(f"成功下載檔案數: {total_success}")
    print(f"失敗檔案數: {total_errors}")
    print(f"成功率: {(total_success/total_attempted*100):.1f}%" if total_attempted > 0 else "N/A")
    
    return results

def main():
    """
    主函數：檢測並下載缺失檔案
    """
    print("=" * 60)
    print("農業站缺失檔案檢測與下載工具")
    print("=" * 60)
    
    # 清理殘留進程
    print("清理殘留的Chrome進程...")
    cleanup_chrome_processes()
    
    # 檢測缺失檔案
    missing_files = check_missing_files()
    
    if not missing_files:
        print("🎉 所有農業站檔案都已完整！")
        return
    
    # 顯示缺失統計
    total_missing = sum(len(months) for months in missing_files.values())
    print(f"發現 {len(missing_files)} 個農業站有缺失檔案")
    print(f"總缺失檔案數: {total_missing}")
    
    # 確認是否開始下載
    print("\n是否開始下載缺失檔案？")
    print("1. 是，開始下載")
    print("2. 否，僅檢測")
    
    choice = input("請選擇 (1-2): ").strip()
    
    if choice == "1":
        try:
            # 開始下載
            results = parallel_download_missing_files(missing_files, max_workers=8)
            
            # 重新檢測
            print("\n重新檢測檔案完整性...")
            final_missing = check_missing_files()
            
            if not final_missing:
                print("🎉 所有檔案下載完成！")
            else:
                remaining_missing = sum(len(months) for months in final_missing.values())
                print(f"⚠️ 仍有 {remaining_missing} 個檔案缺失，可能需要重新運行")
                
        except KeyboardInterrupt:
            print("\n下載被用戶中斷")
        except Exception as e:
            print(f"\n下載過程中發生錯誤: {e}")
        finally:
            print("清理殘留進程...")
            cleanup_chrome_processes()
    else:
        print("僅檢測模式，未進行下載")

if __name__ == "__main__":
    main()
