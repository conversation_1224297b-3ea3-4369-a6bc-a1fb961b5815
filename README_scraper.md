# CODiS 氣象資料爬蟲程式說明

## 📋 程式概述

這個程式用於自動化下載中央氣象署 CODiS (Climate Observation Data Inquiry System) 系統的氣象站月報表資料。

## 🎯 主要功能

1. **智能測站選擇**：根據經緯度座標自動找到最近的氣象測站
2. **自動範圍檢測**：獲取測站的可用資料時間範圍
3. **批量下載**：逐月下載指定時間範圍的月報表資料
4. **進度追蹤**：顯示詳細的下載進度

## 🔧 技術架構

- **Selenium WebDriver**：模擬瀏覽器操作（CODiS 是動態網頁，無法直接 API 存取）
- **Haversine 公式**：計算地理距離找到最近測站
- **自動化日期選擇**：處理複雜的網頁日期選擇器
- **智能檔案下載**：自動設定下載目錄和選項

## 📁 檔案結構

```
data/
├── raw/weather/          # 原始下載的氣象資料
└── interim/stations/     # 測站基本資訊
    └── stations.csv      # 測站座標和代號
```

## 🚀 使用方法

### 基本使用
```python
# 根據經緯度找到最近測站並下載資料
stn_id = get_stn_id(121.3038, 24.4327)  # 桃山地區
download_codis(stn_id)  # 下載 2022/07-2025/06 的資料
```

### 自訂時間範圍
```python
# 下載特定時間範圍的資料
download_codis(stn_id, start_date="2023/01", end_date="2024/12")
```

## 📊 輸出資料

- **檔案格式**：CSV 格式的月報表
- **檔案命名**：由 CODiS 系統自動命名
- **資料內容**：包含溫度、濕度、風速、降雨量等完整氣象資料

## ⚙️ 系統需求

- Python 3.7+
- Chrome 瀏覽器
- 必要套件：selenium, pandas, pathlib

## 🔍 程式流程詳解

### 1. 測站選擇階段
- 讀取或創建測站資料檔案
- 使用 Haversine 公式計算距離
- 回傳最近測站的 ID

### 2. 範圍檢測階段
- 開啟 CODiS 網站
- 選擇測站類型（自動氣象站）
- 輸入測站 ID 並選擇測站
- 檢查可用的年份和月份範圍

### 3. 批量下載階段
- 設定瀏覽器下載選項
- 逐月遍歷指定時間範圍
- 對每個月份：
  - 開啟日期選擇器
  - 選擇年份和月份
  - 點擊下載按鈕
  - 等待檔案下載完成

## 💡 設計考量

### 為什麼使用 Selenium？
- CODiS 是動態網頁，資料通過 JavaScript 載入
- 沒有公開的 API 可以直接存取
- 需要模擬真實的使用者操作

### 為什麼逐月下載？
- CODiS 系統不支援批量下載多個月份
- 每個月的資料都是獨立的 CSV 檔案
- 需要分別選擇年份和月份才能下載

### 錯誤處理機制
- 自動調整下載範圍到測站可用範圍內
- 處理座標資料異常（跳過有問題的測站）
- 等待機制確保頁面元素完全載入

## 🎯 使用場景

- **農業研究**：分析特定地區的氣候變化
- **環境監測**：長期氣象資料收集
- **機器學習**：氣象預測模型訓練資料
- **學術研究**：氣候變遷分析

## 📝 注意事項

1. 下載大量資料時請注意網路穩定性
2. 建議在網路流量較低時執行
3. 程式會自動創建必要的目錄結構
4. 首次運行會創建示例測站資料檔案
