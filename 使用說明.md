# 台灣自動氣象站資料下載程式

## 功能說明

本程式可以自動下載中央氣象署 CODiS 系統中所有自動氣象站的月報表資料。

## 檔案說明

- `scrape_codis_date.py` - 主要的爬蟲程式，包含所有核心功能
- `download_all_taiwan_stations.py` - 批量下載全台灣測站的專用腳本
- `data/interim/stations/stations.csv` - 測站基本資訊檔案

## 使用方法

### 1. 測試單一測站
```bash
python scrape_codis_date.py
# 選擇模式 1，會下載桃山測站的資料
```

### 2. 批量下載已知測站（測試用）
```bash
python scrape_codis_date.py
# 選擇模式 2，會下載3個已知測站的資料
```

### 3. 批量下載全台灣所有測站
```bash
python download_all_taiwan_stations.py
```

## 下載設定

- **時間範圍**：2022年7月 到 2025年7月
- **資料類型**：月報表
- **測站類型**：自動氣象站（代號以 C0 開頭）
- **下載目錄**：`data/raw/weather/all_stations/`

## 重要注意事項

1. **必須使用有視窗的模式**：程式不能使用無頭模式，必須看到瀏覽器視窗才能正確下載
2. **下載時間很長**：全台灣所有測站的資料下載可能需要數小時
3. **保持電腦運行**：下載過程中請勿關閉電腦或瀏覽器
4. **可以中斷重啟**：如果程式被中斷，已下載的檔案會保留，可以重新運行繼續下載

## 檔案結構

下載完成後，檔案會按以下結構組織：

```
data/raw/weather/all_stations/
├── C0A980/
│   ├── C0A980-2022-07.csv
│   ├── C0A980-2022-08.csv
│   └── ...
├── C0B100/
│   ├── C0B100-2022-07.csv
│   └── ...
└── ...
```

## 資料格式

每個 CSV 檔案包含該測站該月份的每日氣象資料，包括：
- 氣壓、氣溫、濕度
- 風速、風向
- 降水量
- 等其他氣象要素

## 故障排除

1. **如果程式卡住**：可以按 Ctrl+C 中斷，然後重新運行
2. **如果某些測站下載失敗**：這是正常的，因為不是所有測站代號都存在
3. **如果檔案下載到錯誤位置**：檢查程式輸出的下載目錄設定

## 系統需求

- Python 3.7+
- Chrome 瀏覽器
- 網路連線
- 足夠的硬碟空間（建議至少 10GB）
