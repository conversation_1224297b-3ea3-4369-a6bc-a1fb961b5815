"""
批量下載全台灣自動氣象站資料
基於原始 scrape_codis_date.py 的架構
"""

from scrape_codis_date import download_codis
import pandas as pd
import os
from pathlib import Path

def create_all_stations_list():
    """
    創建包含更多台灣自動氣象站的清單
    基於 CODiS 系統的 C0 開頭測站代號規則
    """
    
    # 已知存在的測站（從原始程式確認）
    known_stations = [
        {"站號": "C0A980", "站名": "桃山", "緯度": 24.4327, "經度": 121.3038},
        {"站號": "C0B100", "站名": "台北", "緯度": 25.0330, "經度": 121.5654},
        {"站號": "C0C480", "站名": "台中", "緯度": 24.1477, "經度": 120.6736}
    ]
    
    # 可能存在的其他測站代號（基於 CODiS 系統規則）
    # C0 開頭表示自動氣象站
    potential_stations = []
    
    # 生成可能的測站代號範圍
    for prefix in ['C0A', 'C0B', 'C0C', 'C0D', 'C0E', 'C0F', 'C0G', 'C0H']:
        for suffix in range(100, 1000, 10):  # 每10個號碼取一個
            station_id = f"{prefix}{suffix:03d}"
            potential_stations.append({
                "站號": station_id,
                "站名": f"測站{station_id}",
                "緯度": 24.0,  # 預設值
                "經度": 121.0  # 預設值
            })
    
    # 合併已知和可能的測站
    all_stations = known_stations + potential_stations[:50]  # 限制數量避免太多
    
    return pd.DataFrame(all_stations)

def batch_download_all_stations():
    """
    批量下載全台灣自動氣象站的月報表資料
    時間範圍：2022年7月到2025年7月
    """
    print("開始批量下載全台灣自動氣象站資料...")
    print("時間範圍: 2022/07 到 2025/07")
    
    # 創建測站清單
    stations_df = create_all_stations_list()
    total_stations = len(stations_df)
    print(f"共有 {total_stations} 個測站需要嘗試下載")
    
    # 設定下載目錄
    base_download_dir = Path("data/raw/weather/all_stations")
    os.makedirs(base_download_dir, exist_ok=True)
    
    # 記錄下載結果
    success_count = 0
    failed_stations = []
    
    # 逐個測站下載
    for index, row in stations_df.iterrows():
        station_id = row["站號"]
        station_name = row["站名"]
        
        print(f"\n進度: {index + 1}/{total_stations}")
        print(f"正在處理測站: {station_id} ({station_name})")
        
        try:
            # 為每個測站創建子目錄
            station_dir = base_download_dir / station_id
            os.makedirs(station_dir, exist_ok=True)
            
            # 下載該測站的資料
            download_codis(
                stn_id=station_id,
                start_date="2022/07",
                end_date="2025/07",
                download_dir=station_dir
            )
            
            # 檢查是否有檔案被下載
            csv_files = list(station_dir.glob("*.csv"))
            if csv_files:
                success_count += 1
                print(f"測站 {station_id} ({station_name}) 下載完成，共 {len(csv_files)} 個檔案")
            else:
                print(f"測站 {station_id} ({station_name}) 可能不存在或無資料")
                failed_stations.append(f"{station_id} ({station_name}) - 無資料")
            
        except Exception as e:
            print(f"測站 {station_id} ({station_name}) 下載失敗: {e}")
            failed_stations.append(f"{station_id} ({station_name}) - 錯誤: {str(e)[:100]}")
            continue
    
    # 輸出下載結果摘要
    print(f"\n=== 批量下載完成摘要 ===")
    print(f"成功下載: {success_count}/{total_stations} 個測站")
    
    if failed_stations:
        print(f"失敗測站數量: {len(failed_stations)}")
        print("失敗測站清單:")
        for station in failed_stations[:10]:  # 只顯示前10個失敗的
            print(f"  - {station}")
        if len(failed_stations) > 10:
            print(f"  ... 還有 {len(failed_stations) - 10} 個失敗測站")
    
    print(f"所有檔案已下載到: {base_download_dir}")
    
    # 檢查總下載檔案數
    total_files = 0
    total_size = 0
    for station_dir in base_download_dir.iterdir():
        if station_dir.is_dir():
            files = list(station_dir.glob("*.csv"))
            total_files += len(files)
            for file in files:
                total_size += file.stat().st_size
    
    print(f"總計下載: {total_files} 個檔案，總大小: {total_size/1024/1024:.1f} MB")

if __name__ == "__main__":
    batch_download_all_stations()
