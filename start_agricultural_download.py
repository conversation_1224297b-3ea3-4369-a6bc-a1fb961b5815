"""
直接啟動農業站正式下載
使用10個並行視窗
"""

from download_agricultural_stations import parallel_download_agricultural_stations, cleanup_chrome_processes
import sys

def start_full_download():
    """
    啟動完整的農業站下載
    """
    print("=" * 60)
    print("農業氣象站正式下載")
    print("=" * 60)
    print("下載時間範圍: 2023年7月 到 2025年6月")
    print("並行視窗數: 10個")
    print("農業站總數: 133個")
    print()
    
    # 清理殘留進程
    print("清理殘留的Chrome進程...")
    cleanup_chrome_processes()
    
    try:
        print("開始正式下載...")
        results = parallel_download_agricultural_stations(
            start_date="2023/07",
            end_date="2025/06",
            max_workers=10
        )
        
        print("\n=== 下載完成 ===")
        return True
        
    except KeyboardInterrupt:
        print("\n下載被用戶中斷")
        return False
    except Exception as e:
        print(f"\n下載過程中發生錯誤: {e}")
        return False
    finally:
        print("清理殘留進程...")
        cleanup_chrome_processes()

if __name__ == "__main__":
    success = start_full_download()
    sys.exit(0 if success else 1)
