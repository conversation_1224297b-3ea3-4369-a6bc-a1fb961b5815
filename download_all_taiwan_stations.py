"""
批量下載全台灣自動氣象站月報表資料
時間範圍：2022年7月到2025年7月

使用方法：
python download_all_taiwan_stations.py

注意：
- 程式會開啟瀏覽器視窗進行下載（不能使用無頭模式）
- 下載過程可能需要數小時，請保持電腦運行
- 檔案會下載到 data/raw/weather/all_stations/ 目錄下
- 每個測站會有獨立的子目錄
"""

from scrape_codis_date import download_all_stations, generate_potential_stations
import time
from datetime import datetime

def main():
    """
    主函數：批量下載全台灣自動氣象站資料
    """
    print("=" * 60)
    print("全台灣自動氣象站月報表批量下載程式")
    print("=" * 60)
    print()
    print("下載設定：")
    print("- 時間範圍：2022年7月 到 2025年7月")
    print("- 資料類型：月報表")
    print("- 測站類型：自動氣象站（C0開頭）")
    print("- 下載目錄：data/raw/weather/all_stations/")
    print()
    
    # 顯示預計的測站數量
    potential_stations = generate_potential_stations(test_mode=False)
    print(f"預計嘗試下載 {len(potential_stations)} 個潛在測站")
    print("（包含已知存在的測站和可能存在的測站代號）")
    print()
    
    # 估算時間
    estimated_time_per_station = 2  # 每個測站約2分鐘（包含失敗的嘗試）
    estimated_total_minutes = len(potential_stations) * estimated_time_per_station
    estimated_hours = estimated_total_minutes / 60
    
    print(f"預估下載時間：約 {estimated_hours:.1f} 小時")
    print("（實際時間可能因網路狀況和測站資料量而有所不同）")
    print()
    
    # 確認是否繼續
    print("注意事項：")
    print("1. 程式會開啟瀏覽器視窗，請勿關閉")
    print("2. 下載過程中請保持電腦運行")
    print("3. 可以隨時按 Ctrl+C 中斷程式")
    print("4. 已下載的檔案會保留，可以重新運行程式繼續下載")
    print()
    
    confirm = input("確定要開始批量下載嗎？(y/N): ")
    
    if confirm.lower() != 'y':
        print("已取消下載")
        return
    
    # 記錄開始時間
    start_time = datetime.now()
    print(f"\n開始時間：{start_time.strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    try:
        # 開始批量下載
        download_all_stations(
            start_date="2022/07",
            end_date="2025/07",
            test_mode=False
        )
        
        # 記錄結束時間
        end_time = datetime.now()
        duration = end_time - start_time
        
        print("=" * 60)
        print("批量下載完成！")
        print(f"開始時間：{start_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"結束時間：{end_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"總耗時：{duration}")
        print("=" * 60)
        
    except KeyboardInterrupt:
        print("\n\n程式被用戶中斷")
        print("已下載的檔案會保留在 data/raw/weather/all_stations/ 目錄中")
        print("可以重新運行程式繼續下載")
        
    except Exception as e:
        print(f"\n\n程式執行過程中發生錯誤：{e}")
        print("已下載的檔案會保留在 data/raw/weather/all_stations/ 目錄中")
        print("可以重新運行程式繼續下載")

if __name__ == "__main__":
    main()
