"""
批量下載全台灣自動氣象站月報表資料
時間範圍：2022年7月到2025年7月

使用方法：
python download_all_taiwan_stations.py

注意：
- 程式會開啟瀏覽器視窗進行下載（不能使用無頭模式）
- 下載過程可能需要數小時，請保持電腦運行
- 檔案會下載到 data/raw/weather/all_stations/ 目錄下
- 每個測站會有獨立的子目錄
"""

from scrape_codis_date import download_codis, generate_potential_stations
import time
from datetime import datetime
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed
import os
from pathlib import Path

def download_single_station(station_id, start_date, end_date, base_download_dir, thread_id):
    """
    下載單一測站的資料（用於多執行緒）
    """
    try:
        # 為每個測站創建子目錄
        station_dir = base_download_dir / station_id

        print(f"[執行緒 {thread_id}] 正在下載測站: {station_id}")

        # 下載該測站的資料
        download_codis(
            stn_id=station_id,
            start_date=start_date,
            end_date=end_date,
            download_dir=station_dir
        )

        # 檢查是否有檔案被下載（檢查目標目錄和預設下載目錄）
        csv_files_target = list(station_dir.glob("*.csv")) if station_dir.exists() else []

        # 檢查預設下載目錄是否有新檔案
        default_download_dir = Path("D:/Users/<USER>/Downloads")
        csv_files_default = list(default_download_dir.glob(f"{station_id}-*.csv"))

        # 如果檔案在預設下載目錄，移動到目標目錄
        if csv_files_default and not csv_files_target:
            os.makedirs(station_dir, exist_ok=True)
            moved_files = 0
            for file in csv_files_default:
                try:
                    target_file = station_dir / file.name
                    file.rename(target_file)
                    moved_files += 1
                    print(f"[執行緒 {thread_id}] 移動檔案: {file.name} -> {target_file}")
                except Exception as e:
                    print(f"[執行緒 {thread_id}] 移動檔案失敗: {e}")

            if moved_files > 0:
                csv_files_target = list(station_dir.glob("*.csv"))

        total_files = len(csv_files_target) + len(csv_files_default)

        if total_files > 0:
            print(f"[執行緒 {thread_id}] 測站 {station_id} 下載成功，共 {total_files} 個檔案")
            return {"station_id": station_id, "status": "success", "files": total_files}
        else:
            print(f"[執行緒 {thread_id}] 測站 {station_id} 可能不存在或無資料")
            return {"station_id": station_id, "status": "no_data", "files": 0}

    except Exception as e:
        print(f"[執行緒 {thread_id}] 測站 {station_id} 下載失敗: {e}")
        return {"station_id": station_id, "status": "error", "error": str(e)[:100]}

def parallel_download_all_stations(start_date="2022/07", end_date="2025/07", max_workers=5):
    """
    使用多執行緒並行下載全台灣自動氣象站資料
    """
    print("開始並行批量下載全台灣自動氣象站資料...")
    print(f"時間範圍: {start_date} 到 {end_date}")
    print(f"並行執行緒數: {max_workers}")

    # 生成測站清單
    potential_stations = generate_potential_stations(test_mode=False)
    total_stations = len(potential_stations)
    print(f"共有 {total_stations} 個測站需要下載")

    # 設定基礎下載目錄
    base_download_dir = Path("data/raw/weather/all_stations")
    os.makedirs(base_download_dir, exist_ok=True)

    # 記錄結果
    results = []
    completed_count = 0

    # 使用 ThreadPoolExecutor 進行並行下載
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        # 提交所有下載任務
        future_to_station = {
            executor.submit(
                download_single_station,
                station_id,
                start_date,
                end_date,
                base_download_dir,
                i % max_workers + 1  # 執行緒 ID
            ): station_id
            for i, station_id in enumerate(potential_stations)
        }

        # 處理完成的任務
        for future in as_completed(future_to_station):
            station_id = future_to_station[future]
            completed_count += 1

            try:
                result = future.result()
                results.append(result)

                # 顯示進度
                progress = (completed_count / total_stations) * 100
                print(f"進度: {completed_count}/{total_stations} ({progress:.1f}%)")

            except Exception as e:
                print(f"測站 {station_id} 處理時發生錯誤: {e}")
                results.append({"station_id": station_id, "status": "error", "error": str(e)})

    # 統計結果
    success_count = sum(1 for r in results if r["status"] == "success")
    no_data_count = sum(1 for r in results if r["status"] == "no_data")
    error_count = sum(1 for r in results if r["status"] == "error")
    total_files = sum(r.get("files", 0) for r in results if r["status"] == "success")

    print(f"\n=== 並行下載完成摘要 ===")
    print(f"成功下載: {success_count}/{total_stations} 個測站")
    print(f"無資料測站: {no_data_count} 個")
    print(f"失敗測站: {error_count} 個")
    print(f"總下載檔案數: {total_files} 個")
    print(f"所有檔案已下載到: {base_download_dir}")

    return results

def main():
    """
    主函數：批量下載全台灣自動氣象站資料
    """
    print("=" * 60)
    print("全台灣自動氣象站月報表並行批量下載程式")
    print("=" * 60)
    print()
    print("下載設定：")
    print("- 時間範圍：2022年7月 到 2025年7月")
    print("- 資料類型：月報表")
    print("- 測站類型：自動氣象站（C0開頭）")
    print("- 下載目錄：data/raw/weather/all_stations/")
    print("- 並行模式：同時開啟多個瀏覽器視窗")
    print()

    # 顯示預計的測站數量
    potential_stations = generate_potential_stations(test_mode=False)
    print(f"預計下載 {len(potential_stations)} 個官方自動氣象站")
    print("（基於中央氣象署官方測站清單）")
    print()

    # 詢問並行執行緒數
    while True:
        try:
            max_workers = int(input("請輸入並行執行緒數 (建議 3-8，預設 5): ") or "5")
            if 1 <= max_workers <= 10:
                break
            else:
                print("請輸入 1-10 之間的數字")
        except ValueError:
            print("請輸入有效的數字")

    # 估算時間（並行模式）
    estimated_time_per_station = 1.5 / max_workers  # 並行可以大幅縮短時間
    estimated_total_minutes = len(potential_stations) * estimated_time_per_station
    estimated_hours = estimated_total_minutes / 60

    print(f"\n使用 {max_workers} 個並行執行緒")
    print(f"預估下載時間：約 {estimated_hours:.1f} 小時")
    print("（實際時間可能因網路狀況和測站資料量而有所不同）")
    print()
    
    # 確認是否繼續
    print("注意事項：")
    print("1. 程式會開啟瀏覽器視窗，請勿關閉")
    print("2. 下載過程中請保持電腦運行")
    print("3. 可以隨時按 Ctrl+C 中斷程式")
    print("4. 已下載的檔案會保留，可以重新運行程式繼續下載")
    print()
    
    confirm = input("確定要開始批量下載嗎？(y/N): ")
    
    if confirm.lower() != 'y':
        print("已取消下載")
        return
    
    # 記錄開始時間
    start_time = datetime.now()
    print(f"\n開始時間：{start_time.strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    try:
        # 開始並行批量下載
        results = parallel_download_all_stations(
            start_date="2022/07",
            end_date="2025/07",
            max_workers=max_workers
        )
        
        # 記錄結束時間
        end_time = datetime.now()
        duration = end_time - start_time
        
        print("=" * 60)
        print("批量下載完成！")
        print(f"開始時間：{start_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"結束時間：{end_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"總耗時：{duration}")
        print("=" * 60)
        
    except KeyboardInterrupt:
        print("\n\n程式被用戶中斷")
        print("已下載的檔案會保留在 data/raw/weather/all_stations/ 目錄中")
        print("可以重新運行程式繼續下載")
        
    except Exception as e:
        print(f"\n\n程式執行過程中發生錯誤：{e}")
        print("已下載的檔案會保留在 data/raw/weather/all_stations/ 目錄中")
        print("可以重新運行程式繼續下載")

if __name__ == "__main__":
    main()
