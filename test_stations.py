"""
測試程式：簡單測試現有功能
"""

from scrape_codis_date import get_stn_id, download_codis
import os

if __name__ == "__main__":
    print("測試現有功能...")

    # 測試根據經緯度找測站
    print("測試根據經緯度找測站...")
    stn_id = get_stn_id(121.3038, 24.4327)  # 桃山地區
    print(f"找到最近的測站: {stn_id}")

    # 測試下載單個測站的一個月資料
    print(f"\n測試下載測站 {stn_id} 的 2024/01 資料...")
    try:
        download_codis(
            stn_id=stn_id,
            start_date="2024/01",
            end_date="2024/01",
            download_dir="data/raw/weather/test"
        )
        print("測試下載完成")
    except Exception as e:
        print(f"測試下載失敗: {e}")
