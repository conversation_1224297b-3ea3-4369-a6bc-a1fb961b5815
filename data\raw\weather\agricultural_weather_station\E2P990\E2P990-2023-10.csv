﻿"觀測時間(day)","測站氣壓(hPa)","海平面氣壓(hPa)","測站最高氣壓(hPa)","測站最高氣壓時間(LST)","測站最低氣壓(hPa)","測站最低氣壓時間(LST)","氣溫(℃)","最高氣溫(℃)","最高氣溫時間(LST)","最低氣溫(℃)","最低氣溫時間(LST)","露點溫度(℃)","相對溼度(%)","最小相對溼度(%)","最小相對溼度時間(LST)","風速(m/s)","風向(360degree)","最大瞬間風(m/s)","最大瞬間風風向(360degree)","最大瞬間風風速時間(LST)","降水量(mm)","降水時數(hour)","最大十分鐘降水量(mm)","最大十分鐘降水量起始時間(LST)","日照時數(hour)","日照率(%)","全天空日射量(MJ/㎡)","地溫0cm","地溫5cm","地溫10cm","地溫20cm","地溫30cm","地溫50cm","地溫100cm"
"ObsTime","StnPres","SeaPres","StnPresMax","StnPresMaxTime","StnPresMin","StnPresMinTime","Temperature","T Max","T Max Time","T Min","T Min Time","Td dew point","RH","RHMin","RHMinTime","WS","WD","WSGust","WDGust","WGustTime","Precp","PrecpHour","PrecpMax10","PrecpMax10Time","SunShine","SunshineRate","GloblRad","TxSoil0cm","TxSoil5cm","TxSoil10cm","TxSoil20cm","TxSoil30cm","TxSoil50cm","TxSoil100cm"
"01","935.0","1010.6","936.4","2023/10/01 22:36:00","933.2","2023/10/01 15:39:00","24.6","31.1","2023/10/01 13:14:00","22.0","2023/10/01 23:59:00","23.3","93","65","2023/10/01 12:47:00","0.6","180","3.6","250","2023/10/01 13:25:00","0.5","X","0.5","2023/10/01 15:53:00","8.1","X","16.98","26.4","25.8","25.8","25.7","/","26.0","24.5"
"02","935.4","1011.2","937.0","2023/10/02 08:20:00","933.8","2023/10/02 15:30:00","24.0","31.0","2023/10/02 13:22:00","21.2","2023/10/02 04:36:00","23.1","95","64","2023/10/02 13:28:00","0.6","130","4.2","250","2023/10/02 13:19:00","0.0","X","0.0","2023/10/02 00:01:00","6.2","X","17.42","26.2","25.7","25.8","25.7","/","26.1","24.6"
"03","932.7","1008.3","935.0","2023/10/03 00:01:00","930.8","2023/10/03 16:46:00","24.1","28.2","2023/10/03 11:09:00","21.9","2023/10/03 22:54:00","23.3","95","77","2023/10/03 11:05:00","0.5","240","2.7","190","2023/10/03 13:41:00","0.0","X","0.0","2023/10/03 00:01:00","9.1","X","15.07","26.2","25.6","25.8","25.7","/","26.1","24.6"
"04","927.8","1003.2","932.4","2023/10/04 00:01:00","923.2","2023/10/04 23:53:00","23.0","25.9","2023/10/04 09:16:00","21.1","2023/10/04 05:54:00","22.7","98","80","2023/10/04 09:18:00","0.5","130","2.6","180","2023/10/04 21:55:00","0.5","X","0.5","2023/10/04 12:50:00","5.4","X","5.86","25.1","24.7","25.0","25.3","/","26.1","24.7"
"05","925.0","1000.2","932.5","2023/10/05 23:19:00","921.0","2023/10/05 05:35:00","23.1","27.0","2023/10/05 12:21:00","21.5","2023/10/05 23:25:00","22.9","98","72","2023/10/05 12:34:00","0.9","100","9.6","350","2023/10/05 10:38:00","0.5","X","0.5","2023/10/05 16:33:00","6.7","X","7.15","24.8","24.3","24.6","24.8","/","25.9","24.8"
"06","934.8","1011.3","936.5","2023/10/06 21:57:00","932.1","2023/10/06 00:02:00","21.0","23.2","2023/10/06 10:28:00","19.7","2023/10/06 17:53:00","21.0","99","99","2023/10/06 19:54:00","0.6","90","2.8","60","2023/10/06 16:31:00","1.0","X","0.5","2023/10/06 11:38:00","2.1","X","2.87","23.8","23.5","24.0","24.4","/","25.7","24.8"
"07","936.7","1012.9","938.3","2023/10/07 23:16:00","934.8","2023/10/07 03:18:00","22.8","28.0","2023/10/07 12:32:00","20.1","2023/10/07 04:04:00","22.0","95","69","2023/10/07 12:24:00","0.5","100","3.4","240","2023/10/07 11:41:00","0.0","X","0.0","2023/10/07 00:01:00","7.5","X","12.17","24.1","23.5","23.7","23.9","/","25.4","24.8"
"08","938.5","1014.8","940.7","2023/10/08 20:24:00","937.0","2023/10/08 04:16:00","23.2","28.4","2023/10/08 13:55:00","20.7","2023/10/08 05:10:00","22.5","95","76","2023/10/08 13:48:00","0.5","100","3.4","230","2023/10/08 17:42:00","1.5","X","0.5","2023/10/08 17:17:00","8.0","X","10.58","24.4","23.9","24.1","24.2","/","25.3","24.7"
"09","939.9","1016.2","941.9","2023/10/09 20:45:00","938.5","2023/10/09 03:31:00","23.1","28.2","2023/10/09 11:43:00","20.4","2023/10/09 05:27:00","22.4","95","75","2023/10/09 11:06:00","0.7","130","3.8","270","2023/10/09 16:52:00","0.0","X","0.0","2023/10/09 00:01:00","7.7","X","15.34","24.6","24.0","24.2","24.2","/","25.2","24.6"
"10","939.9","1016.4","941.3","2023/10/10 23:17:00","937.9","2023/10/10 14:49:00","22.8","28.4","2023/10/10 11:09:00","20.2","2023/10/10 05:47:00","22.0","95","72","2023/10/10 10:29:00","0.5","140","3.6","280","2023/10/10 13:05:00","0.0","X","0.0","2023/10/10 00:01:00","6.7","X","11.98","24.5","24.0","24.2","24.3","/","25.2","24.6"
"11","940.4","1016.8","941.8","2023/10/11 21:21:00","939.1","2023/10/11 14:48:00","22.9","28.0","2023/10/11 10:58:00","20.3","2023/10/11 05:54:00","22.2","96","72","2023/10/11 10:59:00","0.3","90","2.9","260","2023/10/11 10:55:00","0.5","X","0.5","2023/10/11 03:30:00","5.8","X","11.29","24.5","24.0","24.2","24.3","/","25.2","24.5"
"12","940.7","1017.3","942.2","2023/10/12 09:23:00","939.4","2023/10/12 14:59:00","22.6","27.4","2023/10/12 12:03:00","20.9","2023/10/12 23:41:00","22.1","97","76","2023/10/12 12:40:00","0.4","200","3.3","250","2023/10/12 12:01:00","0.0","X","0.0","2023/10/12 00:01:00","6.3","X","11.76","24.6","24.1","24.2","24.3","/","25.2","24.5"
"13","939.3","1015.8","940.9","2023/10/13 09:02:00","937.5","2023/10/13 14:37:00","22.1","27.0","2023/10/13 11:00:00","20.1","2023/10/14 00:00:00","21.5","96","71","2023/10/13 11:02:00","0.4","110","3.7","210","2023/10/13 16:09:00","0.0","X","0.0","2023/10/13 00:01:00","7.0","X","9.93","24.2","23.8","24.0","24.2","/","25.2","24.5"
"14","937.8","1014.2","939.0","2023/10/14 09:10:00","936.0","2023/10/14 14:46:00","22.3","28.5","2023/10/14 13:48:00","19.2","2023/10/14 06:15:00","21.3","94","62","2023/10/14 13:51:00","0.4","100","2.8","240","2023/10/14 14:07:00","0.5","X","0.5","2023/10/14 09:58:00","8.4","X","12.09","23.8","23.4","23.7","23.9","/","25.2","24.5"
"15","938.3","1014.9","939.7","2023/10/15 23:14:00","936.8","2023/10/15 03:10:00","21.7","25.1","2023/10/15 11:28:00","18.9","2023/10/15 03:24:00","21.1","96","79","2023/10/15 11:09:00","0.4","110","2.6","290","2023/10/15 11:50:00","0.0","X","0.0","2023/10/15 00:01:00","6.8","X","8.60","23.6","23.2","23.5","23.8","/","25.0","24.5"
"16","940.0","1016.9","941.6","2023/10/16 22:51:00","938.8","2023/10/16 15:12:00","21.2","23.5","2023/10/16 12:30:00","19.5","2023/10/17 00:00:00","21.1","98","85","2023/10/16 15:01:00","0.2","90","1.8","120","2023/10/16 19:31:00","0.0","X","0.0","2023/10/16 00:01:00","5.6","X","4.80","23.3","22.9","23.3","23.6","/","24.9","24.4"
"17","941.1","1018.1","942.6","2023/10/17 09:42:00","939.7","2023/10/17 03:43:00","21.0","24.0","2023/10/17 11:31:00","19.4","2023/10/17 00:22:00","20.3","96","76","2023/10/17 12:05:00","0.3","80","1.7","230","2023/10/17 10:33:00","0.0","X","0.0","2023/10/17 00:01:00","6.5","X","6.52","22.8","22.5","22.8","23.2","/","24.8","24.4"
"18","941.8","1018.8","943.5","2023/10/18 09:20:00","940.4","2023/10/18 14:58:00","21.6","25.6","2023/10/18 13:33:00","19.5","2023/10/18 04:30:00","20.5","93","69","2023/10/18 11:24:00","0.5","80","2.7","230","2023/10/18 14:06:00","0.0","X","0.0","2023/10/18 00:01:00","7.5","X","9.97","22.9","22.5","22.8","23.0","/","24.6","24.3"
"19","940.8","1017.6","942.2","2023/10/19 00:01:00","939.3","2023/10/19 14:59:00","21.7","26.6","2023/10/19 10:57:00","19.4","2023/10/19 05:38:00","21.1","96","69","2023/10/19 10:17:00","0.4","110","2.4","220","2023/10/19 12:31:00","0.0","X","0.0","2023/10/19 00:01:00","4.5","X","9.21","23.1","22.6","22.9","23.1","/","24.4","24.2"
"20","940.2","1016.9","941.5","2023/10/20 09:49:00","939.0","2023/10/20 04:21:00","22.1","26.8","2023/10/20 11:34:00","20.0","2023/10/20 00:28:00","21.2","95","71","2023/10/20 11:54:00","0.5","130","3.4","250","2023/10/20 13:30:00","0.0","X","0.0","2023/10/20 00:01:00","5.9","X","11.45","23.4","22.8","23.0","23.2","/","24.4","24.1"
"21","941.8","1018.7","943.4","2023/10/21 21:25:00","940.0","2023/10/21 04:18:00","21.6","26.0","2023/10/21 10:44:00","19.4","2023/10/21 05:47:00","20.9","96","72","2023/10/21 10:44:00","0.4","80","2.2","250","2023/10/21 11:46:00","0.5","X","0.5","2023/10/21 20:10:00","5.7","X","8.28","23.2","22.8","23.0","23.2","/","24.4","24.1"
"22","942.4","1019.1","943.7","2023/10/22 09:36:00","941.0","2023/10/22 15:27:00","22.4","29.1","2023/10/22 14:33:00","19.0","2023/10/22 01:04:00","20.8","92","64","2023/10/22 14:04:00","0.5","210","3.5","250","2023/10/22 12:56:00","0.5","X","0.5","2023/10/22 17:56:00","7.2","X","19.08","23.6","22.9","23.1","23.2","/","24.3","24.0"
"23","942.0","1018.9","943.1","2023/10/23 08:25:00","940.7","2023/10/23 14:54:00","21.7","27.2","2023/10/23 12:54:00","18.7","2023/10/23 05:48:00","21.0","96","73","2023/10/23 11:51:00","0.5","110","3.1","230","2023/10/23 13:07:00","1.0","X","0.5","2023/10/23 10:34:00","6.0","X","12.96","23.3","22.9","23.2","23.4","/","24.4","24.0"
"24","941.2","1017.8","943.8","2023/10/24 08:58:00","939.2","2023/10/24 15:33:00","22.5","29.0","2023/10/24 13:23:00","19.1","2023/10/24 04:54:00","21.4","94","68","2023/10/24 13:22:00","0.6","140","3.6","240","2023/10/24 13:22:00","0.5","X","0.5","2023/10/24 20:39:00","7.2","X","16.55","23.7","23.1","23.3","23.3","/","24.4","24.0"
"25","940.1","1016.6","941.5","2023/10/25 00:01:00","938.5","2023/10/25 14:48:00","23.0","28.9","2023/10/25 13:24:00","19.8","2023/10/25 23:49:00","21.7","93","65","2023/10/25 13:15:00","0.6","200","3.5","250","2023/10/25 13:00:00","0.0","X","0.0","2023/10/25 00:01:00","6.7","X","16.59","24.3","23.7","23.7","23.6","/","24.4","23.9"
"26","939.7","1016.3","940.8","2023/10/26 08:13:00","938.4","2023/10/26 15:22:00","21.9","28.2","2023/10/26 13:25:00","19.0","2023/10/26 06:33:00","20.6","92","62","2023/10/26 13:25:00","0.7","150","5.1","230","2023/10/26 17:22:00","0.0","X","0.0","2023/10/26 00:01:00","6.7","X","14.67","23.9","23.4","23.6","23.7","/","24.6","23.9"
"27","939.1","1015.9","940.6","2023/10/27 09:07:00","937.7","2023/10/27 15:23:00","21.3","26.4","2023/10/27 12:53:00","18.7","2023/10/27 05:34:00","20.3","94","68","2023/10/27 11:43:00","0.5","140","3.9","260","2023/10/27 15:59:00","0.0","X","0.0","2023/10/27 00:01:00","5.9","X","12.00","23.2","22.8","23.2","23.4","/","24.6","24.0"
"28","939.4","1016.1","941.1","2023/10/28 09:42:00","937.9","2023/10/28 15:16:00","21.8","27.3","2023/10/28 13:46:00","19.4","2023/10/28 08:02:00","20.9","95","66","2023/10/28 13:47:00","0.3","220","3.1","280","2023/10/28 12:40:00","0.0","X","0.0","2023/10/28 00:01:00","6.8","X","12.56","23.4","22.9","23.1","23.3","/","24.5","24.0"
"29","940.3","1017.2","941.8","2023/10/29 21:41:00","938.9","2023/10/29 15:01:00","21.0","26.9","2023/10/29 14:49:00","18.3","2023/10/29 05:20:00","20.1","95","69","2023/10/29 14:19:00","0.5","140","3.9","250","2023/10/29 14:31:00","0.0","X","0.0","2023/10/29 00:01:00","7.1","X","10.73","23.0","22.6","22.9","23.2","/","24.4","23.9"
"30","940.9","1017.8","942.6","2023/10/30 23:32:00","939.4","2023/10/30 14:34:00","21.4","27.3","2023/10/30 13:42:00","18.3","2023/10/30 04:42:00","20.1","93","64","2023/10/30 10:54:00","0.5","110","3.5","260","2023/10/30 14:19:00","0.0","X","0.0","2023/10/30 00:01:00","7.8","X","14.17","22.9","22.5","22.8","23.0","/","24.3","23.9"
"31","941.9","1019.0","943.2","2023/10/31 22:46:00","940.5","2023/10/31 13:59:00","21.0","26.3","2023/10/31 11:13:00","18.2","2023/10/31 05:54:00","20.0","94","68","2023/10/31 11:13:00","0.5","100","2.5","210","2023/10/31 11:16:00","0.5","X","0.5","2023/10/31 08:38:00","6.1","X","11.26","22.7","22.3","22.6","22.9","/","24.2","23.9"